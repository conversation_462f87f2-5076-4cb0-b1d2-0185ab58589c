version: '3.8'

services:
  vm-orchestrator:
    build: .
    container_name: vm-orchestrator
    ports:
      - "8080:8080"
    volumes:
      - ./config.yaml:/app/config.yaml:ro
      - ./logs:/app/logs
    environment:
      - VMO_SERVER_MODE=release
      - VMO_LOGGING_LEVEL=info
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - vm-orchestrator-network

  # Optional: Add a reverse proxy
  nginx:
    image: nginx:alpine
    container_name: vm-orchestrator-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - vm-orchestrator
    restart: unless-stopped
    networks:
      - vm-orchestrator-network

networks:
  vm-orchestrator-network:
    driver: bridge

volumes:
  logs:
    driver: local
