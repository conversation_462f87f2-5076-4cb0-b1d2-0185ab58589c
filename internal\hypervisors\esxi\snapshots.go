package esxi

import (
	"context"
	"time"

	"vm-orchestrator/internal/models"

	"github.com/vmware/govmomi/vim25/types"
)

// ListSnapshots returns a list of snapshots for a VM
func (c *Client) ListSnapshots(ctx context.Context, vmID string) ([]models.Snapshot, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return nil, models.NewNotFoundError("VM", vmID)
	}

	var vmProps struct {
		Snapshot *types.VirtualMachineSnapshotInfo `mo:"snapshot"`
	}
	err = vm.Properties(ctx, vm.Reference(), []string{"snapshot"}, &vmProps)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get VM snapshots", err.Error())
	}

	var snapshots []models.Snapshot
	if vmProps.Snapshot != nil {
		snapshots = c.extractSnapshots(vmID, vmProps.Snapshot.RootSnapshotList, "")
	}

	return snapshots, nil
}

// GetSnapshot returns a specific snapshot
func (c *Client) GetSnapshot(ctx context.Context, snapshotID string) (*models.Snapshot, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	// Find VM that contains this snapshot
	vms, err := c.finder.VirtualMachineList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list VMs", err.Error())
	}

	for _, vm := range vms {
		var vmProps struct {
			Snapshot *types.VirtualMachineSnapshotInfo `mo:"snapshot"`
		}
		err = vm.Properties(ctx, vm.Reference(), []string{"snapshot"}, &vmProps)
		if err != nil {
			continue
		}

		if vmProps.Snapshot != nil {
			snapshot := c.findSnapshotByID(vm.Reference().Value, vmProps.Snapshot.RootSnapshotList, snapshotID)
			if snapshot != nil {
				return snapshot, nil
			}
		}
	}

	return nil, models.NewNotFoundError("Snapshot", snapshotID)
}

// CreateSnapshot creates a new snapshot
func (c *Client) CreateSnapshot(ctx context.Context, vmID string, req models.SnapshotCreateRequest) (*models.Snapshot, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return nil, models.NewNotFoundError("VM", vmID)
	}

	// Create snapshot
	task, err := vm.CreateSnapshot(ctx, req.Name, req.Description, req.Memory, req.Quiesce)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Failed to create snapshot", err.Error())
	}

	// Wait for task completion
	info, err := task.WaitForResult(ctx, nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Snapshot creation task failed", err.Error())
	}

	// Get created snapshot reference
	snapshotRef := info.Result.(types.ManagedObjectReference)

	// Get VM name for the snapshot
	var vmProps struct {
		Name string `mo:"name"`
	}
	err = vm.Properties(ctx, vm.Reference(), []string{"name"}, &vmProps)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get VM name", err.Error())
	}

	snapshot := &models.Snapshot{
		ID:           snapshotRef.Value,
		Name:         req.Name,
		Description:  req.Description,
		VMID:         vmID,
		VMName:       vmProps.Name,
		HypervisorID: c.config.ID,
		Hypervisor:   "esxi",
		State:        models.SnapshotStateReady,
		SizeGB:       0, // Would need additional API calls to get size
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	return snapshot, nil
}

// RestoreSnapshot restores a VM to a snapshot
func (c *Client) RestoreSnapshot(ctx context.Context, snapshotID string, req models.SnapshotRestoreRequest) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	// Find VM and snapshot
	vms, err := c.finder.VirtualMachineList(ctx, "*")
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list VMs", err.Error())
	}

	for _, vm := range vms {
		var vmProps struct {
			Snapshot *types.VirtualMachineSnapshotInfo `mo:"snapshot"`
		}
		err = vm.Properties(ctx, vm.Reference(), []string{"snapshot"}, &vmProps)
		if err != nil {
			continue
		}

		if vmProps.Snapshot != nil {
			snapshotRef := c.findSnapshotRefByID(vmProps.Snapshot.RootSnapshotList, snapshotID)
			if snapshotRef != nil {
				// Power off VM if requested
				if req.PowerOff {
					state, err := vm.PowerState(ctx)
					if err == nil && state == types.VirtualMachinePowerStatePoweredOn {
						task, err := vm.PowerOff(ctx)
						if err == nil {
							task.WaitForResult(ctx, nil)
						}
					}
				}

				// Revert to snapshot
				task, err := vm.RevertToSnapshot(ctx, snapshotRef.Value, req.PowerOff)
				if err != nil {
					return models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Failed to restore snapshot", err.Error())
				}

				_, err = task.WaitForResult(ctx, nil)
				if err != nil {
					return models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Snapshot restore task failed", err.Error())
				}

				return nil
			}
		}
	}

	return models.NewNotFoundError("Snapshot", snapshotID)
}

// DeleteSnapshot deletes a snapshot
func (c *Client) DeleteSnapshot(ctx context.Context, snapshotID string) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	// Find VM and snapshot
	vms, err := c.finder.VirtualMachineList(ctx, "*")
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list VMs", err.Error())
	}

	for _, vm := range vms {
		var vmProps struct {
			Snapshot *types.VirtualMachineSnapshotInfo `mo:"snapshot"`
		}
		err = vm.Properties(ctx, vm.Reference(), []string{"snapshot"}, &vmProps)
		if err != nil {
			continue
		}

		if vmProps.Snapshot != nil {
			snapshotRef := c.findSnapshotRefByID(vmProps.Snapshot.RootSnapshotList, snapshotID)
			if snapshotRef != nil {
				// Remove snapshot
				task, err := vm.RemoveSnapshot(ctx, snapshotRef.Value, false, nil)
				if err != nil {
					return models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Failed to delete snapshot", err.Error())
				}

				_, err = task.WaitForResult(ctx, nil)
				if err != nil {
					return models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Snapshot deletion task failed", err.Error())
				}

				return nil
			}
		}
	}

	return models.NewNotFoundError("Snapshot", snapshotID)
}

// GetSnapshotTree returns the snapshot tree for a VM
func (c *Client) GetSnapshotTree(ctx context.Context, vmID string) (*models.SnapshotTree, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return nil, models.NewNotFoundError("VM", vmID)
	}

	var vmProps struct {
		Snapshot *types.VirtualMachineSnapshotInfo `mo:"snapshot"`
	}
	err = vm.Properties(ctx, vm.Reference(), []string{"snapshot"}, &vmProps)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get VM snapshots", err.Error())
	}

	if vmProps.Snapshot == nil || len(vmProps.Snapshot.RootSnapshotList) == 0 {
		return &models.SnapshotTree{}, nil
	}

	// Build tree from root snapshots
	tree := c.buildSnapshotTree(vmID, vmProps.Snapshot.RootSnapshotList[0], "")
	return tree, nil
}

// Helper methods for snapshot operations

// extractSnapshots recursively extracts snapshots from the snapshot tree
func (c *Client) extractSnapshots(vmID string, snapshotList []types.VirtualMachineSnapshotTree, parentID string) []models.Snapshot {
	var snapshots []models.Snapshot

	for _, snap := range snapshotList {
		snapshot := models.Snapshot{
			ID:           snap.Snapshot.Value,
			Name:         snap.Name,
			Description:  snap.Description,
			VMID:         vmID,
			HypervisorID: c.config.ID,
			Hypervisor:   "esxi",
			State:        models.SnapshotStateReady,
			ParentID:     parentID,
			CreatedAt:    snap.CreateTime,
			UpdatedAt:    snap.CreateTime,
		}

		// Extract child snapshot IDs
		for _, child := range snap.ChildSnapshotList {
			snapshot.Children = append(snapshot.Children, child.Snapshot.Value)
		}

		snapshots = append(snapshots, snapshot)

		// Recursively extract child snapshots
		childSnapshots := c.extractSnapshots(vmID, snap.ChildSnapshotList, snap.Snapshot.Value)
		snapshots = append(snapshots, childSnapshots...)
	}

	return snapshots
}

// findSnapshotByID finds a snapshot by ID in the snapshot tree
func (c *Client) findSnapshotByID(vmID string, snapshotList []types.VirtualMachineSnapshotTree, snapshotID string) *models.Snapshot {
	for _, snap := range snapshotList {
		if snap.Snapshot.Value == snapshotID {
			snapshot := &models.Snapshot{
				ID:           snap.Snapshot.Value,
				Name:         snap.Name,
				Description:  snap.Description,
				VMID:         vmID,
				HypervisorID: c.config.ID,
				Hypervisor:   "esxi",
				State:        models.SnapshotStateReady,
				CreatedAt:    snap.CreateTime,
				UpdatedAt:    snap.CreateTime,
			}
			return snapshot
		}

		// Search in child snapshots
		if found := c.findSnapshotByID(vmID, snap.ChildSnapshotList, snapshotID); found != nil {
			return found
		}
	}

	return nil
}

// findSnapshotRefByID finds a snapshot reference by ID
func (c *Client) findSnapshotRefByID(snapshotList []types.VirtualMachineSnapshotTree, snapshotID string) *types.ManagedObjectReference {
	for _, snap := range snapshotList {
		if snap.Snapshot.Value == snapshotID {
			return &snap.Snapshot
		}

		// Search in child snapshots
		if found := c.findSnapshotRefByID(snap.ChildSnapshotList, snapshotID); found != nil {
			return found
		}
	}

	return nil
}

// buildSnapshotTree builds a hierarchical snapshot tree
func (c *Client) buildSnapshotTree(vmID string, snap types.VirtualMachineSnapshotTree, parentID string) *models.SnapshotTree {
	snapshot := &models.Snapshot{
		ID:           snap.Snapshot.Value,
		Name:         snap.Name,
		Description:  snap.Description,
		VMID:         vmID,
		HypervisorID: c.config.ID,
		Hypervisor:   "esxi",
		State:        models.SnapshotStateReady,
		ParentID:     parentID,
		CreatedAt:    snap.CreateTime,
		UpdatedAt:    snap.CreateTime,
	}

	tree := &models.SnapshotTree{
		Snapshot: snapshot,
	}

	// Build child trees
	for _, child := range snap.ChildSnapshotList {
		childTree := c.buildSnapshotTree(vmID, child, snap.Snapshot.Value)
		tree.Children = append(tree.Children, childTree)
	}

	return tree
}
