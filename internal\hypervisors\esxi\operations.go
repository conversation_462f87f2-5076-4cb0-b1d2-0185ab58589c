package esxi

import (
	"context"
	"fmt"
	"strings"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/types"
)

// UpdateVM updates an existing virtual machine
func (c *Client) UpdateVM(ctx context.Context, vmID string, req models.VMUpdateRequest) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return nil, models.NewNotFoundError("VM", vmID)
	}

	// Build configuration spec
	spec := types.VirtualMachineConfigSpec{}
	
	if req.Name != nil {
		spec.Name = *req.Name
	}
	
	if req.CPU != nil {
		spec.NumCPUs = int32(req.CPU.Cores)
	}
	
	if req.Memory != nil {
		spec.MemoryMB = int64(req.Memory.SizeMB)
	}

	// Reconfigure VM
	task, err := vm.Reconfigure(ctx, spec)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to update VM", err.Error())
	}

	// Wait for task completion
	_, err = task.WaitForResult(ctx, nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "VM update task failed", err.Error())
	}

	return c.convertVMToModel(ctx, vm)
}

// DeleteVM deletes a virtual machine
func (c *Client) DeleteVM(ctx context.Context, vmID string, force bool) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return models.NewNotFoundError("VM", vmID)
	}

	// Check VM state
	state, err := vm.PowerState(ctx)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get VM state", err.Error())
	}

	// Power off VM if it's running and force is true
	if state == types.VirtualMachinePowerStatePoweredOn {
		if !force {
			return models.NewAPIError(models.ErrorCodeVMInvalidState, "VM is powered on", "Use force=true to power off and delete")
		}
		
		task, err := vm.PowerOff(ctx)
		if err != nil {
			return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to power off VM", err.Error())
		}
		
		_, err = task.WaitForResult(ctx, nil)
		if err != nil {
			return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Power off task failed", err.Error())
		}
	}

	// Delete VM
	task, err := vm.Destroy(ctx)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to delete VM", err.Error())
	}

	_, err = task.WaitForResult(ctx, nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "VM deletion task failed", err.Error())
	}

	return nil
}

// StartVM powers on a virtual machine
func (c *Client) StartVM(ctx context.Context, vmID string) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return models.NewNotFoundError("VM", vmID)
	}

	task, err := vm.PowerOn(ctx)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to start VM", err.Error())
	}

	_, err = task.WaitForResult(ctx, nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Start VM task failed", err.Error())
	}

	return nil
}

// StopVM powers off a virtual machine
func (c *Client) StopVM(ctx context.Context, vmID string, force bool) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return models.NewNotFoundError("VM", vmID)
	}

	var task *object.Task
	if force {
		task, err = vm.PowerOff(ctx)
	} else {
		err = vm.ShutdownGuest(ctx)
		if err != nil {
			// Fallback to power off if guest shutdown fails
			task, err = vm.PowerOff(ctx)
		}
	}

	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to stop VM", err.Error())
	}

	if task != nil {
		_, err = task.WaitForResult(ctx, nil)
		if err != nil {
			return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Stop VM task failed", err.Error())
		}
	}

	return nil
}

// RestartVM restarts a virtual machine
func (c *Client) RestartVM(ctx context.Context, vmID string, force bool) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return models.NewNotFoundError("VM", vmID)
	}

	if force {
		task, err := vm.Reset(ctx)
		if err != nil {
			return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to restart VM", err.Error())
		}
		
		_, err = task.WaitForResult(ctx, nil)
		if err != nil {
			return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Restart VM task failed", err.Error())
		}
	} else {
		err = vm.RebootGuest(ctx)
		if err != nil {
			return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to restart VM", err.Error())
		}
	}

	return nil
}

// SuspendVM suspends a virtual machine
func (c *Client) SuspendVM(ctx context.Context, vmID string) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return models.NewNotFoundError("VM", vmID)
	}

	task, err := vm.Suspend(ctx)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to suspend VM", err.Error())
	}

	_, err = task.WaitForResult(ctx, nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Suspend VM task failed", err.Error())
	}

	return nil
}

// ResumeVM resumes a suspended virtual machine
func (c *Client) ResumeVM(ctx context.Context, vmID string) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return models.NewNotFoundError("VM", vmID)
	}

	task, err := vm.PowerOn(ctx)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to resume VM", err.Error())
	}

	_, err = task.WaitForResult(ctx, nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Resume VM task failed", err.Error())
	}

	return nil
}

// GetVMMetrics returns performance metrics for a VM
func (c *Client) GetVMMetrics(ctx context.Context, vmID string) (*models.VMMetrics, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return nil, models.NewNotFoundError("VM", vmID)
	}

	// Get VM summary for basic metrics
	var vmObj object.VirtualMachine
	err = vm.Properties(ctx, vm.Reference(), []string{"summary"}, &vmObj)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get VM properties", err.Error())
	}

	summary := vmObj.Summary
	
	metrics := &models.VMMetrics{
		VMID:      vmID,
		Timestamp: time.Now(),
		CPU: models.CPUMetrics{
			UsagePercent: float64(summary.QuickStats.OverallCpuUsage) / float64(summary.Config.CpuReservation) * 100,
			UsageMHz:     float64(summary.QuickStats.OverallCpuUsage),
		},
		Memory: models.MemoryMetrics{
			UsagePercent: float64(summary.QuickStats.GuestMemoryUsage) / float64(summary.Config.MemorySizeMB) * 100,
			UsageMB:      float64(summary.QuickStats.GuestMemoryUsage),
			TotalMB:      float64(summary.Config.MemorySizeMB),
		},
	}

	return metrics, nil
}

// GetVMConsoleURL returns console URL for a VM
func (c *Client) GetVMConsoleURL(ctx context.Context, vmID string) (string, error) {
	if !c.connected {
		return "", models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return "", models.NewNotFoundError("VM", vmID)
	}

	// Generate console URL (simplified - in real implementation would use proper console ticket)
	return fmt.Sprintf("https://%s/ui/webconsole.html?vmId=%s", c.config.Host, vm.Reference().Value), nil
}
