package tests

import (
	"os"
	"testing"

	"vm-orchestrator/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConfigDefaults(t *testing.T) {
	// Create a temporary config file with minimal content
	configContent := `
hypervisors:
  esxi:
    - name: "test-esxi"
      host: "*************"
      username: "root"
      password: "password"
`
	
	tmpFile, err := os.CreateTemp("", "config-*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())
	
	_, err = tmpFile.WriteString(configContent)
	require.NoError(t, err)
	tmpFile.Close()
	
	// Set environment variable to use our test config
	os.Setenv("VMO_CONFIG_FILE", tmpFile.Name())
	defer os.Unsetenv("VMO_CONFIG_FILE")
	
	cfg, err := config.Load()
	require.NoError(t, err)
	
	// Test default values
	assert.Equal(t, 8080, cfg.Server.Port)
	assert.Equal(t, "debug", cfg.Server.Mode)
	assert.Equal(t, 30, cfg.Server.ReadTimeout)
	assert.Equal(t, 30, cfg.Server.WriteTimeout)
	assert.Equal(t, 60, cfg.Server.IdleTimeout)
	
	assert.Equal(t, "info", cfg.Logging.Level)
	assert.Equal(t, "json", cfg.Logging.Format)
	assert.Equal(t, "stdout", cfg.Logging.Output)
	
	assert.Equal(t, "sqlite", cfg.Database.Type)
	assert.Equal(t, "localhost", cfg.Database.Host)
	assert.Equal(t, 5432, cfg.Database.Port)
	assert.Equal(t, "vm_orchestrator", cfg.Database.Database)
	assert.Equal(t, "disable", cfg.Database.SSLMode)
	
	assert.False(t, cfg.Security.EnableAuth)
	assert.False(t, cfg.Security.EnableHTTPS)
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.Config
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid config",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: 8080,
					Mode: "debug",
				},
				Logging: config.LoggingConfig{
					Level:  "info",
					Format: "json",
				},
				Hypervisors: config.HypervisorsConfig{
					ESXi: []config.ESXiConfig{
						{
							Name:     "test-esxi",
							Host:     "*************",
							Username: "root",
							Password: "password",
						},
					},
				},
			},
			expectError: false,
		},
		{
			name: "Invalid server port",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: 70000,
					Mode: "debug",
				},
				Logging: config.LoggingConfig{
					Level:  "info",
					Format: "json",
				},
				Hypervisors: config.HypervisorsConfig{
					ESXi: []config.ESXiConfig{
						{
							Name:     "test-esxi",
							Host:     "*************",
							Username: "root",
							Password: "password",
						},
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid server port",
		},
		{
			name: "Invalid server mode",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: 8080,
					Mode: "invalid",
				},
				Logging: config.LoggingConfig{
					Level:  "info",
					Format: "json",
				},
				Hypervisors: config.HypervisorsConfig{
					ESXi: []config.ESXiConfig{
						{
							Name:     "test-esxi",
							Host:     "*************",
							Username: "root",
							Password: "password",
						},
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid server mode",
		},
		{
			name: "Invalid log level",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: 8080,
					Mode: "debug",
				},
				Logging: config.LoggingConfig{
					Level:  "invalid",
					Format: "json",
				},
				Hypervisors: config.HypervisorsConfig{
					ESXi: []config.ESXiConfig{
						{
							Name:     "test-esxi",
							Host:     "*************",
							Username: "root",
							Password: "password",
						},
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid log level",
		},
		{
			name: "No hypervisors",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: 8080,
					Mode: "debug",
				},
				Logging: config.LoggingConfig{
					Level:  "info",
					Format: "json",
				},
				Hypervisors: config.HypervisorsConfig{},
			},
			expectError: true,
			errorMsg:    "at least one hypervisor must be configured",
		},
		{
			name: "ESXi missing name",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: 8080,
					Mode: "debug",
				},
				Logging: config.LoggingConfig{
					Level:  "info",
					Format: "json",
				},
				Hypervisors: config.HypervisorsConfig{
					ESXi: []config.ESXiConfig{
						{
							Host:     "*************",
							Username: "root",
							Password: "password",
						},
					},
				},
			},
			expectError: true,
			errorMsg:    "name is required",
		},
		{
			name: "ESXi missing host",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: 8080,
					Mode: "debug",
				},
				Logging: config.LoggingConfig{
					Level:  "info",
					Format: "json",
				},
				Hypervisors: config.HypervisorsConfig{
					ESXi: []config.ESXiConfig{
						{
							Name:     "test-esxi",
							Username: "root",
							Password: "password",
						},
					},
				},
			},
			expectError: true,
			errorMsg:    "host is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Use reflection to call the private validateConfig function
			// For this test, we'll create a simple validation function
			err := validateTestConfig(tt.config)
			
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// validateTestConfig is a simplified version of the validation logic for testing
func validateTestConfig(cfg *config.Config) error {
	// Server validation
	if cfg.Server.Port < 1 || cfg.Server.Port > 65535 {
		return assert.AnError
	}
	
	if cfg.Server.Mode != "debug" && cfg.Server.Mode != "release" {
		return assert.AnError
	}
	
	// Logging validation
	validLogLevels := []string{"trace", "debug", "info", "warn", "error", "fatal", "panic"}
	if !contains(validLogLevels, cfg.Logging.Level) {
		return assert.AnError
	}
	
	// Hypervisor validation
	if len(cfg.Hypervisors.ESXi) == 0 && len(cfg.Hypervisors.Proxmox) == 0 {
		return assert.AnError
	}
	
	// ESXi validation
	for _, esxi := range cfg.Hypervisors.ESXi {
		if esxi.Name == "" || esxi.Host == "" || esxi.Username == "" || esxi.Password == "" {
			return assert.AnError
		}
	}
	
	// Proxmox validation
	for _, proxmox := range cfg.Hypervisors.Proxmox {
		if proxmox.Name == "" || proxmox.Host == "" || proxmox.Username == "" || proxmox.Password == "" {
			return assert.AnError
		}
	}
	
	return nil
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func TestGetConfigExample(t *testing.T) {
	example := config.GetConfigExample()
	
	assert.NotEmpty(t, example)
	assert.Contains(t, example, "server:")
	assert.Contains(t, example, "logging:")
	assert.Contains(t, example, "hypervisors:")
	assert.Contains(t, example, "esxi:")
	assert.Contains(t, example, "proxmox:")
	assert.Contains(t, example, "database:")
	assert.Contains(t, example, "security:")
}
