package interfaces

import (
	"context"
	"vm-orchestrator/internal/models"
)

// HypervisorClient defines the interface that all hypervisor implementations must satisfy
type HypervisorClient interface {
	// Connection management
	Connect(ctx context.Context) error
	Disconnect(ctx context.Context) error
	IsConnected() bool
	GetInfo() *HypervisorInfo

	// VM lifecycle operations
	ListVMs(ctx context.Context, filters VMFilters) ([]models.VirtualMachine, error)
	GetVM(ctx context.Context, vmID string) (*models.VirtualMachine, error)
	CreateVM(ctx context.Context, req models.VMCreateRequest) (*models.VirtualMachine, error)
	UpdateVM(ctx context.Context, vmID string, req models.VMUpdateRequest) (*models.VirtualMachine, error)
	DeleteVM(ctx context.Context, vmID string, force bool) error

	// VM power operations
	StartVM(ctx context.Context, vmID string) error
	StopVM(ctx context.Context, vmID string, force bool) error
	RestartVM(ctx context.Context, vmID string, force bool) error
	SuspendVM(ctx context.Context, vmID string) error
	ResumeVM(ctx context.Context, vmID string) error

	// VM monitoring
	GetVMMetrics(ctx context.Context, vmID string) (*models.VMMetrics, error)
	GetVMConsoleURL(ctx context.Context, vmID string) (string, error)

	// Host operations
	ListHosts(ctx context.Context) ([]models.Host, error)
	GetHost(ctx context.Context, hostID string) (*models.Host, error)
	GetHostMetrics(ctx context.Context, hostID string) (*models.HostMetrics, error)

	// Template operations
	ListTemplates(ctx context.Context, filters TemplateFilters) ([]models.Template, error)
	GetTemplate(ctx context.Context, templateID string) (*models.Template, error)
	CreateTemplate(ctx context.Context, req models.TemplateCreateRequest) (*models.Template, error)
	UpdateTemplate(ctx context.Context, templateID string, req models.TemplateUpdateRequest) (*models.Template, error)
	DeleteTemplate(ctx context.Context, templateID string) error
	DeployTemplate(ctx context.Context, templateID string, req models.TemplateDeployRequest) (*models.VirtualMachine, error)

	// Snapshot operations
	ListSnapshots(ctx context.Context, vmID string) ([]models.Snapshot, error)
	GetSnapshot(ctx context.Context, snapshotID string) (*models.Snapshot, error)
	CreateSnapshot(ctx context.Context, vmID string, req models.SnapshotCreateRequest) (*models.Snapshot, error)
	RestoreSnapshot(ctx context.Context, snapshotID string, req models.SnapshotRestoreRequest) error
	DeleteSnapshot(ctx context.Context, snapshotID string) error
	GetSnapshotTree(ctx context.Context, vmID string) (*models.SnapshotTree, error)

	// Storage operations
	ListDatastores(ctx context.Context) ([]DatastoreInfo, error)
	GetDatastore(ctx context.Context, datastoreID string) (*DatastoreInfo, error)

	// Network operations
	ListNetworks(ctx context.Context) ([]NetworkInfo, error)
	GetNetwork(ctx context.Context, networkID string) (*NetworkInfo, error)
}

// HypervisorInfo contains basic information about the hypervisor
type HypervisorInfo struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"` // "esxi" or "proxmox"
	Version string `json:"version"`
	Address string `json:"address"`
}

// VMFilters represents filters for VM listing
type VMFilters struct {
	Name         string            `json:"name,omitempty"`
	State        models.VMState    `json:"state,omitempty"`
	HypervisorID string            `json:"hypervisor_id,omitempty"`
	Tags         map[string]string `json:"tags,omitempty"`
	Limit        int               `json:"limit,omitempty"`
	Offset       int               `json:"offset,omitempty"`
}

// TemplateFilters represents filters for template listing
type TemplateFilters struct {
	Name         string            `json:"name,omitempty"`
	OS           string            `json:"os,omitempty"`
	HypervisorID string            `json:"hypervisor_id,omitempty"`
	Tags         map[string]string `json:"tags,omitempty"`
	Limit        int               `json:"limit,omitempty"`
	Offset       int               `json:"offset,omitempty"`
}

// DatastoreInfo represents information about a datastore
type DatastoreInfo struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Type         string  `json:"type"`
	TotalGB      int     `json:"total_gb"`
	FreeGB       int     `json:"free_gb"`
	UsedGB       int     `json:"used_gb"`
	UsagePercent float64 `json:"usage_percent"`
	Accessible   bool    `json:"accessible"`
}

// NetworkInfo represents information about a network
type NetworkInfo struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Type   string `json:"type"`
	VLAN   int    `json:"vlan,omitempty"`
	Active bool   `json:"active"`
}

// HypervisorFactory defines the interface for creating hypervisor clients
type HypervisorFactory interface {
	CreateClient(config HypervisorConfig) (HypervisorClient, error)
	GetSupportedTypes() []string
}

// HypervisorConfig represents configuration for connecting to a hypervisor
type HypervisorConfig struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"` // "esxi" or "proxmox"
	Host     string `json:"host"`
	Port     int    `json:"port,omitempty"`
	Username string `json:"username"`
	Password string `json:"password"`
	Insecure bool   `json:"insecure,omitempty"`
	Timeout  int    `json:"timeout,omitempty"` // seconds
}
