#!/usr/bin/env python3
"""
VM Orchestrator - Python Example
This script demonstrates various VM operations using the REST API
"""

import requests
import json
import time
import sys
from typing import Dict, List, Optional

class VMOrchestrator:
    def __init__(self, base_url: str = "http://localhost:8080/api/v1"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """Make HTTP request to the API"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"API request failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Response text: {e.response.text}")
            sys.exit(1)
    
    def check_health(self) -> Dict:
        """Check API health"""
        return self._make_request('GET', '/../health')
    
    def list_hypervisors(self) -> List[Dict]:
        """List all connected hypervisors"""
        response = self._make_request('GET', '/hypervisors')
        return response['data']
    
    def list_vms(self, hypervisor_id: Optional[str] = None, **filters) -> List[Dict]:
        """List virtual machines"""
        endpoint = '/vms'
        if hypervisor_id:
            endpoint = f'/hypervisors/{hypervisor_id}/vms'
        
        # Add query parameters
        params = []
        for key, value in filters.items():
            if value is not None:
                params.append(f"{key}={value}")
        
        if params:
            endpoint += '?' + '&'.join(params)
        
        response = self._make_request('GET', endpoint)
        return response['data']['vms']
    
    def get_vm(self, hypervisor_id: str, vm_id: str) -> Dict:
        """Get specific VM details"""
        response = self._make_request('GET', f'/hypervisors/{hypervisor_id}/vms/{vm_id}')
        return response['data']
    
    def create_vm(self, vm_config: Dict) -> Dict:
        """Create a new virtual machine"""
        response = self._make_request('POST', '/vms', vm_config)
        return response['data']
    
    def update_vm(self, hypervisor_id: str, vm_id: str, updates: Dict) -> Dict:
        """Update VM configuration"""
        response = self._make_request('PUT', f'/hypervisors/{hypervisor_id}/vms/{vm_id}', updates)
        return response['data']
    
    def delete_vm(self, hypervisor_id: str, vm_id: str, force: bool = False) -> None:
        """Delete a virtual machine"""
        endpoint = f'/hypervisors/{hypervisor_id}/vms/{vm_id}'
        if force:
            endpoint += '?force=true'
        self._make_request('DELETE', endpoint)
    
    def vm_action(self, hypervisor_id: str, vm_id: str, action: str, force: bool = False) -> Dict:
        """Perform action on VM (start, stop, restart, suspend, resume)"""
        data = {
            'action': action,
            'force': force
        }
        response = self._make_request('POST', f'/hypervisors/{hypervisor_id}/vms/{vm_id}/actions', data)
        return response['data']
    
    def get_vm_metrics(self, hypervisor_id: str, vm_id: str) -> Dict:
        """Get VM performance metrics"""
        response = self._make_request('GET', f'/hypervisors/{hypervisor_id}/vms/{vm_id}/metrics')
        return response['data']
    
    def create_snapshot(self, hypervisor_id: str, vm_id: str, snapshot_config: Dict) -> Dict:
        """Create VM snapshot"""
        response = self._make_request('POST', f'/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots', snapshot_config)
        return response['data']
    
    def list_snapshots(self, hypervisor_id: str, vm_id: str) -> List[Dict]:
        """List VM snapshots"""
        response = self._make_request('GET', f'/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots')
        return response['data']['snapshots']

def main():
    """Main example function"""
    print("VM Orchestrator Python Example")
    print("=" * 40)
    
    # Initialize client
    client = VMOrchestrator()
    
    # Check API health
    print("1. Checking API health...")
    health = client.check_health()
    print(f"   Status: {health['data']['status']}")
    print(f"   Hypervisors: {health['data']['hypervisors']}")
    
    # List hypervisors
    print("\n2. Listing hypervisors...")
    hypervisors = client.list_hypervisors()
    for hv in hypervisors:
        print(f"   - {hv['name']} ({hv['type']}) - {hv['address']}")
    
    if not hypervisors:
        print("   No hypervisors found!")
        return
    
    # Use first hypervisor for examples
    hypervisor_id = hypervisors[0]['id']
    print(f"\n3. Using hypervisor: {hypervisor_id}")
    
    # List VMs
    print("\n4. Listing VMs...")
    vms = client.list_vms(hypervisor_id)
    for vm in vms:
        print(f"   - {vm['name']} ({vm['id']}) - {vm['state']}")
    
    # Example VM configuration
    vm_config = {
        "name": "python-example-vm",
        "hypervisor_id": hypervisor_id,
        "cpu": {
            "cores": 1,
            "sockets": 1,
            "threads": 1
        },
        "memory": {
            "size_mb": 1024
        },
        "disks": [
            {
                "name": "disk0",
                "size_gb": 10,
                "datastore": "datastore1",
                "type": "thin"
            }
        ],
        "networks": [
            {
                "name": "net0",
                "network": "VM Network",
                "type": "vmxnet3"
            }
        ]
    }
    
    # Create VM (commented out to avoid creating VMs in example)
    # print("\n5. Creating example VM...")
    # new_vm = client.create_vm(vm_config)
    # print(f"   Created VM: {new_vm['name']} ({new_vm['id']})")
    
    # If we have VMs, demonstrate operations on the first one
    if vms:
        vm = vms[0]
        vm_id = vm['id']
        
        print(f"\n5. Getting details for VM: {vm['name']}")
        vm_details = client.get_vm(hypervisor_id, vm_id)
        print(f"   State: {vm_details['state']}")
        print(f"   CPU: {vm_details['cpu']['cores']} cores")
        print(f"   Memory: {vm_details['memory']['size_mb']} MB")
        
        print(f"\n6. Getting metrics for VM: {vm['name']}")
        try:
            metrics = client.get_vm_metrics(hypervisor_id, vm_id)
            print(f"   CPU Usage: {metrics['cpu']['usage_percent']:.1f}%")
            print(f"   Memory Usage: {metrics['memory']['usage_percent']:.1f}%")
        except Exception as e:
            print(f"   Could not get metrics: {e}")
        
        print(f"\n7. Listing snapshots for VM: {vm['name']}")
        try:
            snapshots = client.list_snapshots(hypervisor_id, vm_id)
            if snapshots:
                for snapshot in snapshots:
                    print(f"   - {snapshot['name']} ({snapshot['created_at']})")
            else:
                print("   No snapshots found")
        except Exception as e:
            print(f"   Could not list snapshots: {e}")
    
    print("\nExample completed successfully!")

if __name__ == "__main__":
    main()
