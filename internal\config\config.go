package config

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server       ServerConfig       `mapstructure:"server"`
	Logging      LoggingConfig      `mapstructure:"logging"`
	Hypervisors  HypervisorsConfig  `mapstructure:"hypervisors"`
	Database     DatabaseConfig     `mapstructure:"database"`
	Security     SecurityConfig     `mapstructure:"security"`
}

// ServerConfig represents HTTP server configuration
type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	Mode         string `mapstructure:"mode"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
}

// LoggingConfig represents logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// HypervisorsConfig represents hypervisor configurations
type HypervisorsConfig struct {
	ESXi    []ESXiConfig    `mapstructure:"esxi"`
	Proxmox []ProxmoxConfig `mapstructure:"proxmox"`
}

// ESXiConfig represents ESXi hypervisor configuration
type ESXiConfig struct {
	Name     string `mapstructure:"name"`
	Host     string `mapstructure:"host"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Insecure bool   `mapstructure:"insecure"`
}

// ProxmoxConfig represents Proxmox hypervisor configuration
type ProxmoxConfig struct {
	Name     string `mapstructure:"name"`
	Host     string `mapstructure:"host"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Insecure bool   `mapstructure:"insecure"`
}

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Type     string `mapstructure:"type"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Database string `mapstructure:"database"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	SSLMode  string `mapstructure:"ssl_mode"`
}

// SecurityConfig represents security configuration
type SecurityConfig struct {
	JWTSecret     string `mapstructure:"jwt_secret"`
	APIKeys       []string `mapstructure:"api_keys"`
	EnableAuth    bool   `mapstructure:"enable_auth"`
	EnableHTTPS   bool   `mapstructure:"enable_https"`
	CertFile      string `mapstructure:"cert_file"`
	KeyFile       string `mapstructure:"key_file"`
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	// Set default values
	setDefaults()

	// Set configuration file name and paths
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	viper.AddConfigPath("/etc/vm-orchestrator")

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvPrefix("VMO")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read configuration file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %v", err)
		}
		// Config file not found, continue with defaults and env vars
	}

	// Unmarshal configuration
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}

	// Validate configuration
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %v", err)
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.read_timeout", 30)
	viper.SetDefault("server.write_timeout", 30)
	viper.SetDefault("server.idle_timeout", 60)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")

	// Database defaults
	viper.SetDefault("database.type", "sqlite")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.database", "vm_orchestrator")
	viper.SetDefault("database.ssl_mode", "disable")

	// Security defaults
	viper.SetDefault("security.enable_auth", false)
	viper.SetDefault("security.enable_https", false)
}

// validateConfig validates the configuration
func validateConfig(config *Config) error {
	// Validate server configuration
	if config.Server.Port < 1 || config.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", config.Server.Port)
	}

	if config.Server.Mode != "debug" && config.Server.Mode != "release" {
		return fmt.Errorf("invalid server mode: %s (must be 'debug' or 'release')", config.Server.Mode)
	}

	// Validate logging configuration
	validLogLevels := []string{"trace", "debug", "info", "warn", "error", "fatal", "panic"}
	if !contains(validLogLevels, config.Logging.Level) {
		return fmt.Errorf("invalid log level: %s", config.Logging.Level)
	}

	validLogFormats := []string{"json", "text"}
	if !contains(validLogFormats, config.Logging.Format) {
		return fmt.Errorf("invalid log format: %s", config.Logging.Format)
	}

	// Validate hypervisor configurations
	if len(config.Hypervisors.ESXi) == 0 && len(config.Hypervisors.Proxmox) == 0 {
		return fmt.Errorf("at least one hypervisor must be configured")
	}

	// Validate ESXi configurations
	for i, esxi := range config.Hypervisors.ESXi {
		if esxi.Name == "" {
			return fmt.Errorf("ESXi configuration %d: name is required", i)
		}
		if esxi.Host == "" {
			return fmt.Errorf("ESXi configuration %d: host is required", i)
		}
		if esxi.Username == "" {
			return fmt.Errorf("ESXi configuration %d: username is required", i)
		}
		if esxi.Password == "" {
			return fmt.Errorf("ESXi configuration %d: password is required", i)
		}
	}

	// Validate Proxmox configurations
	for i, proxmox := range config.Hypervisors.Proxmox {
		if proxmox.Name == "" {
			return fmt.Errorf("Proxmox configuration %d: name is required", i)
		}
		if proxmox.Host == "" {
			return fmt.Errorf("Proxmox configuration %d: host is required", i)
		}
		if proxmox.Username == "" {
			return fmt.Errorf("Proxmox configuration %d: username is required", i)
		}
		if proxmox.Password == "" {
			return fmt.Errorf("Proxmox configuration %d: password is required", i)
		}
	}

	// Validate security configuration
	if config.Security.EnableHTTPS {
		if config.Security.CertFile == "" {
			return fmt.Errorf("cert_file is required when HTTPS is enabled")
		}
		if config.Security.KeyFile == "" {
			return fmt.Errorf("key_file is required when HTTPS is enabled")
		}

		// Check if certificate files exist
		if _, err := os.Stat(config.Security.CertFile); os.IsNotExist(err) {
			return fmt.Errorf("certificate file does not exist: %s", config.Security.CertFile)
		}
		if _, err := os.Stat(config.Security.KeyFile); os.IsNotExist(err) {
			return fmt.Errorf("key file does not exist: %s", config.Security.KeyFile)
		}
	}

	if config.Security.EnableAuth && config.Security.JWTSecret == "" {
		return fmt.Errorf("jwt_secret is required when authentication is enabled")
	}

	return nil
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetConfigExample returns an example configuration
func GetConfigExample() string {
	return `# VM Orchestrator Configuration

server:
  port: 8080
  mode: debug  # debug or release
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60

logging:
  level: info  # trace, debug, info, warn, error, fatal, panic
  format: json  # json or text
  output: stdout  # stdout, stderr, or file path

hypervisors:
  esxi:
    - name: "esxi-host-1"
      host: "*************"
      username: "root"
      password: "password"
      insecure: true
    - name: "esxi-host-2"
      host: "*************"
      username: "root"
      password: "password"
      insecure: true

  proxmox:
    - name: "proxmox-host-1"
      host: "*************"
      username: "root@pam"
      password: "password"
      insecure: true
    - name: "proxmox-host-2"
      host: "*************"
      username: "root@pam"
      password: "password"
      insecure: true

database:
  type: sqlite  # sqlite, postgres, mysql
  host: localhost
  port: 5432
  database: vm_orchestrator
  username: ""
  password: ""
  ssl_mode: disable

security:
  enable_auth: false
  enable_https: false
  jwt_secret: ""
  api_keys: []
  cert_file: ""
  key_file: ""
`
}
