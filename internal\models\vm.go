package models

import (
	"time"
)

// VMState represents the current state of a virtual machine
type VMState string

const (
	VMStatePoweredOn  VMState = "poweredOn"
	VMStatePoweredOff VMState = "poweredOff"
	VMStateSuspended  VMState = "suspended"
	VMStateUnknown    VMState = "unknown"
)

// VirtualMachine represents a virtual machine across different hypervisors
type VirtualMachine struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	State        VMState           `json:"state"`
	HypervisorID string            `json:"hypervisor_id"`
	Hypervisor   string            `json:"hypervisor"` // "esxi" or "proxmox"
	CPU          CPUConfig         `json:"cpu"`
	Memory       MemoryConfig      `json:"memory"`
	Disks        []DiskConfig      `json:"disks"`
	Networks     []NetworkConfig   `json:"networks"`
	Tags         map[string]string `json:"tags,omitempty"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// CPUConfig represents CPU configuration
type CPUConfig struct {
	Cores   int `json:"cores"`
	Sockets int `json:"sockets"`
	Threads int `json:"threads"`
}

// MemoryConfig represents memory configuration
type MemoryConfig struct {
	SizeMB int `json:"size_mb"`
}

// DiskConfig represents disk configuration
type DiskConfig struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	SizeGB     int    `json:"size_gb"`
	Datastore  string `json:"datastore"`
	Type       string `json:"type"` // "thin", "thick", etc.
	Controller string `json:"controller"`
}

// NetworkConfig represents network adapter configuration
type NetworkConfig struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Network string `json:"network"`
	MAC     string `json:"mac"`
	Type    string `json:"type"`
}

// VMCreateRequest represents a request to create a new VM
type VMCreateRequest struct {
	Name         string            `json:"name" binding:"required"`
	HypervisorID string            `json:"hypervisor_id" binding:"required"`
	Template     string            `json:"template,omitempty"`
	CPU          CPUConfig         `json:"cpu" binding:"required"`
	Memory       MemoryConfig      `json:"memory" binding:"required"`
	Disks        []DiskConfig      `json:"disks"`
	Networks     []NetworkConfig   `json:"networks"`
	Tags         map[string]string `json:"tags,omitempty"`
}

// VMUpdateRequest represents a request to update VM configuration
type VMUpdateRequest struct {
	Name     *string           `json:"name,omitempty"`
	CPU      *CPUConfig        `json:"cpu,omitempty"`
	Memory   *MemoryConfig     `json:"memory,omitempty"`
	Tags     map[string]string `json:"tags,omitempty"`
}

// VMActionRequest represents a request to perform an action on a VM
type VMActionRequest struct {
	Action string `json:"action" binding:"required"` // start, stop, restart, suspend, resume
	Force  bool   `json:"force,omitempty"`
}

// VMListResponse represents a paginated list of VMs
type VMListResponse struct {
	VMs        []VirtualMachine `json:"vms"`
	Total      int              `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// VMMetrics represents performance metrics for a VM
type VMMetrics struct {
	VMID      string    `json:"vm_id"`
	Timestamp time.Time `json:"timestamp"`
	CPU       CPUMetrics    `json:"cpu"`
	Memory    MemoryMetrics `json:"memory"`
	Disk      DiskMetrics   `json:"disk"`
	Network   NetworkMetrics `json:"network"`
}

// CPUMetrics represents CPU performance metrics
type CPUMetrics struct {
	UsagePercent float64 `json:"usage_percent"`
	UsageMHz     float64 `json:"usage_mhz"`
}

// MemoryMetrics represents memory performance metrics
type MemoryMetrics struct {
	UsagePercent float64 `json:"usage_percent"`
	UsageMB      float64 `json:"usage_mb"`
	TotalMB      float64 `json:"total_mb"`
}

// DiskMetrics represents disk performance metrics
type DiskMetrics struct {
	ReadIOPS    float64 `json:"read_iops"`
	WriteIOPS   float64 `json:"write_iops"`
	ReadMBps    float64 `json:"read_mbps"`
	WriteMBps   float64 `json:"write_mbps"`
	UsagePercent float64 `json:"usage_percent"`
}

// NetworkMetrics represents network performance metrics
type NetworkMetrics struct {
	RxMBps float64 `json:"rx_mbps"`
	TxMBps float64 `json:"tx_mbps"`
	RxPPS  float64 `json:"rx_pps"`
	TxPPS  float64 `json:"tx_pps"`
}
