# VM Orchestrator Configuration Example

server:
  port: 8080
  mode: debug  # debug or release
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60

logging:
  level: info  # trace, debug, info, warn, error, fatal, panic
  format: json  # json or text
  output: stdout  # stdout, stderr, or file path

hypervisors:
  esxi:
    - name: "esxi-host-1"
      host: "*************"
      username: "root"
      password: "password"
      insecure: true
    - name: "esxi-host-2"
      host: "*************"
      username: "root"
      password: "password"
      insecure: true

  proxmox:
    - name: "proxmox-host-1"
      host: "*************"
      username: "root@pam"
      password: "password"
      insecure: true
    - name: "proxmox-host-2"
      host: "*************"
      username: "root@pam"
      password: "password"
      insecure: true

database:
  type: sqlite  # sqlite, postgres, mysql
  host: localhost
  port: 5432
  database: vm_orchestrator
  username: ""
  password: ""
  ssl_mode: disable

security:
  enable_auth: false
  enable_https: false
  jwt_secret: ""
  api_keys: []
  cert_file: ""
  key_file: ""
