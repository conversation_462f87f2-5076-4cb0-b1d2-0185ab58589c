package tests

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"vm-orchestrator/internal/api"
	"vm-orchestrator/internal/config"
	"vm-orchestrator/internal/logging"
	"vm-orchestrator/internal/models"
	"vm-orchestrator/internal/orchestrator"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestAPI(t *testing.T) (*gin.Engine, *api.Handler) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test configuration
	cfg := &config.Config{
		Server: config.ServerConfig{
			Port: 8080,
			Mode: "test",
		},
		Logging: config.LoggingConfig{
			Level:  "error", // Reduce log noise in tests
			Format: "json",
			Output: "stdout",
		},
		Hypervisors: config.HypervisorsConfig{
			// No real hypervisors for unit tests
		},
	}

	// Create logger
	logger := logging.NewLogger(cfg.Logging)

	// Create orchestrator (will have no clients for unit tests)
	orch, err := orchestrator.New(cfg, logger)
	require.NoError(t, err)

	// Create API handler
	handler := api.NewHandler(orch, logger)

	// Create router
	router := gin.New()
	api.SetupRoutes(router, handler)

	return router, handler
}

func TestHealthEndpoint(t *testing.T) {
	router, _ := setupTestAPI(t)

	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.NotNil(t, response.Data)

	// Check response data structure
	data, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "healthy", data["status"])
	assert.Contains(t, data, "timestamp")
	assert.Contains(t, data, "hypervisors")
	assert.Contains(t, data, "version")
}

func TestListHypervisors(t *testing.T) {
	router, _ := setupTestAPI(t)

	req, _ := http.NewRequest("GET", "/api/v1/hypervisors", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.NotNil(t, response.Data)

	// Should return empty array since no hypervisors are configured
	hypervisors, ok := response.Data.([]interface{})
	require.True(t, ok)
	assert.Empty(t, hypervisors)
}

func TestListVMs(t *testing.T) {
	router, _ := setupTestAPI(t)

	req, _ := http.NewRequest("GET", "/api/v1/vms", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response.Success)
	assert.NotNil(t, response.Data)

	// Check response structure
	data, ok := response.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Contains(t, data, "vms")
	assert.Contains(t, data, "total")
	assert.Contains(t, data, "page")
	assert.Contains(t, data, "page_size")
	assert.Contains(t, data, "total_pages")
}

func TestCreateVMValidation(t *testing.T) {
	router, _ := setupTestAPI(t)

	// Test with invalid JSON
	req, _ := http.NewRequest("POST", "/api/v1/vms", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.NotNil(t, response.Error)
	assert.Equal(t, models.ErrorCodeInvalidRequest, response.Error.Code)
}

func TestCreateVMWithValidData(t *testing.T) {
	router, _ := setupTestAPI(t)

	vmRequest := models.VMCreateRequest{
		Name:         "test-vm",
		HypervisorID: "test-hypervisor",
		CPU: models.CPUConfig{
			Cores:   2,
			Sockets: 1,
			Threads: 1,
		},
		Memory: models.MemoryConfig{
			SizeMB: 2048,
		},
		Disks: []models.DiskConfig{
			{
				Name:      "disk0",
				SizeGB:    20,
				Datastore: "datastore1",
				Type:      "thin",
			},
		},
		Networks: []models.NetworkConfig{
			{
				Name:    "net0",
				Network: "VM Network",
				Type:    "vmxnet3",
			},
		},
	}

	jsonData, _ := json.Marshal(vmRequest)
	req, _ := http.NewRequest("POST", "/api/v1/vms", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should fail because hypervisor doesn't exist, but validation should pass
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.NotNil(t, response.Error)
}

func TestAPIDocumentation(t *testing.T) {
	router, _ := setupTestAPI(t)

	req, _ := http.NewRequest("GET", "/api/docs", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var docs map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &docs)
	require.NoError(t, err)

	assert.Contains(t, docs, "title")
	assert.Contains(t, docs, "version")
	assert.Contains(t, docs, "description")
	assert.Contains(t, docs, "endpoints")
	assert.Equal(t, "VM Orchestrator API", docs["title"])
}

func TestRequestIDMiddleware(t *testing.T) {
	router, _ := setupTestAPI(t)

	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// Check that request ID header is present
	requestID := w.Header().Get("X-Request-ID")
	assert.NotEmpty(t, requestID)

	// Check that request ID is in response
	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, requestID, response.RequestID)
}

func TestErrorHandling(t *testing.T) {
	router, _ := setupTestAPI(t)

	// Test non-existent endpoint
	req, _ := http.NewRequest("GET", "/api/v1/nonexistent", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)
}

func TestVMActionValidation(t *testing.T) {
	router, _ := setupTestAPI(t)

	// Test with invalid action
	actionRequest := models.VMActionRequest{
		Action: "invalid_action",
		Force:  false,
	}

	jsonData, _ := json.Marshal(actionRequest)
	req, _ := http.NewRequest("POST", "/api/v1/hypervisors/test/vms/test/actions", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response.Success)
	assert.NotNil(t, response.Error)
	assert.Equal(t, models.ErrorCodeInvalidRequest, response.Error.Code)
	assert.Contains(t, response.Error.Message, "Invalid action")
}

func TestQueryParameterParsing(t *testing.T) {
	router, _ := setupTestAPI(t)

	// Test with query parameters
	req, _ := http.NewRequest("GET", "/api/v1/vms?name=test&state=poweredOn&limit=10&offset=0", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response.Success)
}
