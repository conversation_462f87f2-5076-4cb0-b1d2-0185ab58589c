package api

import (
	"net/http"
	"strconv"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/logging"
	"vm-orchestrator/internal/models"
	"vm-orchestrator/internal/orchestrator"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// <PERSON><PERSON> handles HTTP requests for the VM orchestrator API
type Handler struct {
	orchestrator *orchestrator.Orchestrator
	logger       *logrus.Logger
}

// NewHandler creates a new API handler
func NewHandler(orch *orchestrator.Orchestrator, logger *logrus.Logger) *Handler {
	return &Handler{
		orchestrator: orch,
		logger:       logger,
	}
}

// respondWithError sends an error response
func (h *Handler) respondWithError(c *gin.Context, err error) {
	var apiErr *models.APIError
	var ok bool

	if apiErr, ok = err.(*models.APIError); !ok {
		apiErr = models.NewInternalError("Internal server error", err.Error())
	}

	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		apiErr.RequestID = requestID.(string)
	}

	response := models.NewErrorResponse(apiErr)
	response.RequestID = apiErr.RequestID
	response.Timestamp = time.Now().UTC().Format(time.RFC3339)

	// Log error
	logging.ErrorLogger(h.logger, err, map[string]interface{}{
		"request_id": apiErr.RequestID,
		"path":       c.Request.URL.Path,
		"method":     c.Request.Method,
	})

	c.JSON(apiErr.HTTPStatusCode(), response)
}

// respondWithSuccess sends a success response
func (h *Handler) respondWithSuccess(c *gin.Context, data interface{}) {
	response := models.NewSuccessResponse(data)

	if requestID, exists := c.Get("request_id"); exists {
		response.RequestID = requestID.(string)
	}

	response.Timestamp = time.Now().UTC().Format(time.RFC3339)

	c.JSON(http.StatusOK, response)
}

// Health returns the health status of the service
func (h *Handler) Health(c *gin.Context) {
	hypervisors := h.orchestrator.GetHypervisorInfo()

	status := map[string]interface{}{
		"status":      "healthy",
		"timestamp":   time.Now().UTC().Format(time.RFC3339),
		"hypervisors": len(hypervisors),
		"version":     "1.0.0",
	}

	h.respondWithSuccess(c, status)
}

// ListVMs returns a list of virtual machines
func (h *Handler) ListVMs(c *gin.Context) {
	hypervisorID := c.Query("hypervisor_id")

	// Parse filters
	filters := interfaces.VMFilters{
		Name:         c.Query("name"),
		HypervisorID: c.Query("hypervisor_id"),
	}

	if state := c.Query("state"); state != "" {
		filters.State = models.VMState(state)
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filters.Limit = l
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			filters.Offset = o
		}
	}

	vms, err := h.orchestrator.ListVMs(c.Request.Context(), hypervisorID, filters)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	// Build paginated response
	total := len(vms)
	page := 1
	pageSize := filters.Limit
	if pageSize == 0 {
		pageSize = total
	} else {
		page = (filters.Offset / pageSize) + 1
	}

	totalPages := 1
	if pageSize > 0 {
		totalPages = (total + pageSize - 1) / pageSize
	}

	response := models.VMListResponse{
		VMs:        vms,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	h.respondWithSuccess(c, response)
}

// GetVM returns a specific virtual machine
func (h *Handler) GetVM(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	vm, err := h.orchestrator.GetVM(c.Request.Context(), hypervisorID, vmID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, vm)
}

// CreateVM creates a new virtual machine
func (h *Handler) CreateVM(c *gin.Context) {
	var req models.VMCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	vm, err := h.orchestrator.CreateVM(c.Request.Context(), req)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	c.JSON(http.StatusCreated, models.NewSuccessResponse(vm))
}

// UpdateVM updates an existing virtual machine
func (h *Handler) UpdateVM(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	var req models.VMUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	vm, err := h.orchestrator.UpdateVM(c.Request.Context(), hypervisorID, vmID, req)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, vm)
}

// DeleteVM deletes a virtual machine
func (h *Handler) DeleteVM(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")
	force := c.Query("force") == "true"

	err := h.orchestrator.DeleteVM(c.Request.Context(), hypervisorID, vmID, force)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// VMAction performs an action on a virtual machine
func (h *Handler) VMAction(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	var req models.VMActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	var err error
	switch req.Action {
	case "start":
		err = h.orchestrator.StartVM(c.Request.Context(), hypervisorID, vmID)
	case "stop":
		err = h.orchestrator.StopVM(c.Request.Context(), hypervisorID, vmID, req.Force)
	case "restart":
		err = h.orchestrator.RestartVM(c.Request.Context(), hypervisorID, vmID, req.Force)
	case "suspend":
		err = h.orchestrator.SuspendVM(c.Request.Context(), hypervisorID, vmID)
	case "resume":
		err = h.orchestrator.ResumeVM(c.Request.Context(), hypervisorID, vmID)
	default:
		h.respondWithError(c, models.NewValidationError("Invalid action", "Supported actions: start, stop, restart, suspend, resume"))
		return
	}

	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, map[string]string{
		"message": "Action completed successfully",
		"action":  req.Action,
	})
}

// GetVMMetrics returns performance metrics for a VM
func (h *Handler) GetVMMetrics(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	metrics, err := h.orchestrator.GetVMMetrics(c.Request.Context(), hypervisorID, vmID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, metrics)
}

// GetVMConsole returns console URL for a VM
func (h *Handler) GetVMConsole(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	url, err := h.orchestrator.GetVMConsoleURL(c.Request.Context(), hypervisorID, vmID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, map[string]string{
		"console_url": url,
	})
}

// ListHosts returns a list of hypervisor hosts
func (h *Handler) ListHosts(c *gin.Context) {
	hypervisorID := c.Query("hypervisor_id")

	hosts, err := h.orchestrator.ListHosts(c.Request.Context(), hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	response := models.HostListResponse{
		Hosts:      hosts,
		Total:      len(hosts),
		Page:       1,
		PageSize:   len(hosts),
		TotalPages: 1,
	}

	h.respondWithSuccess(c, response)
}

// GetHost returns a specific host
func (h *Handler) GetHost(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	hostID := c.Param("host_id")

	host, err := h.orchestrator.GetHost(c.Request.Context(), hypervisorID, hostID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, host)
}

// GetHostMetrics returns performance metrics for a host
func (h *Handler) GetHostMetrics(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	hostID := c.Param("host_id")

	metrics, err := h.orchestrator.GetHostMetrics(c.Request.Context(), hypervisorID, hostID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, metrics)
}

// GetHypervisors returns a list of connected hypervisors
func (h *Handler) GetHypervisors(c *gin.Context) {
	hypervisors := h.orchestrator.GetHypervisorInfo()
	h.respondWithSuccess(c, hypervisors)
}

// ListTemplates returns a list of templates
func (h *Handler) ListTemplates(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")

	// Parse filters
	filters := interfaces.TemplateFilters{
		Name:         c.Query("name"),
		OS:           c.Query("os"),
		HypervisorID: hypervisorID,
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filters.Limit = l
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			filters.Offset = o
		}
	}

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	templates, err := client.ListTemplates(c.Request.Context(), filters)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	// Build paginated response
	total := len(templates)
	page := 1
	pageSize := filters.Limit
	if pageSize == 0 {
		pageSize = total
	} else {
		page = (filters.Offset / pageSize) + 1
	}

	totalPages := 1
	if pageSize > 0 {
		totalPages = (total + pageSize - 1) / pageSize
	}

	response := models.TemplateListResponse{
		Templates:  templates,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	h.respondWithSuccess(c, response)
}

// GetTemplate returns a specific template
func (h *Handler) GetTemplate(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	templateID := c.Param("template_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	template, err := client.GetTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, template)
}

// CreateTemplate creates a new template
func (h *Handler) CreateTemplate(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")

	var req models.TemplateCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	req.HypervisorID = hypervisorID

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	template, err := client.CreateTemplate(c.Request.Context(), req)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	c.JSON(http.StatusCreated, models.NewSuccessResponse(template))
}

// UpdateTemplate updates an existing template
func (h *Handler) UpdateTemplate(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	templateID := c.Param("template_id")

	var req models.TemplateUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	template, err := client.UpdateTemplate(c.Request.Context(), templateID, req)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, template)
}

// DeleteTemplate deletes a template
func (h *Handler) DeleteTemplate(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	templateID := c.Param("template_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	err = client.DeleteTemplate(c.Request.Context(), templateID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// DeployTemplate deploys a VM from a template
func (h *Handler) DeployTemplate(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	templateID := c.Param("template_id")

	var req models.TemplateDeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	req.HypervisorID = hypervisorID

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	vm, err := client.DeployTemplate(c.Request.Context(), templateID, req)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	c.JSON(http.StatusCreated, models.NewSuccessResponse(vm))
}

// ListSnapshots returns a list of snapshots for a VM
func (h *Handler) ListSnapshots(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	snapshots, err := client.ListSnapshots(c.Request.Context(), vmID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	response := models.SnapshotListResponse{
		Snapshots:  snapshots,
		Total:      len(snapshots),
		Page:       1,
		PageSize:   len(snapshots),
		TotalPages: 1,
	}

	h.respondWithSuccess(c, response)
}

// GetSnapshot returns a specific snapshot
func (h *Handler) GetSnapshot(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	snapshotID := c.Param("snapshot_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	snapshot, err := client.GetSnapshot(c.Request.Context(), snapshotID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, snapshot)
}

// CreateSnapshot creates a new snapshot
func (h *Handler) CreateSnapshot(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	var req models.SnapshotCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	snapshot, err := client.CreateSnapshot(c.Request.Context(), vmID, req)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	c.JSON(http.StatusCreated, models.NewSuccessResponse(snapshot))
}

// RestoreSnapshot restores a VM to a snapshot
func (h *Handler) RestoreSnapshot(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	snapshotID := c.Param("snapshot_id")

	var req models.SnapshotRestoreRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.respondWithError(c, models.NewValidationError("Invalid request body", err.Error()))
		return
	}

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	err = client.RestoreSnapshot(c.Request.Context(), snapshotID, req)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, map[string]string{
		"message": "Snapshot restored successfully",
	})
}

// DeleteSnapshot deletes a snapshot
func (h *Handler) DeleteSnapshot(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	snapshotID := c.Param("snapshot_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	err = client.DeleteSnapshot(c.Request.Context(), snapshotID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// GetSnapshotTree returns the snapshot tree for a VM
func (h *Handler) GetSnapshotTree(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	vmID := c.Param("vm_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	tree, err := client.GetSnapshotTree(c.Request.Context(), vmID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, tree)
}

// ListDatastores returns a list of datastores
func (h *Handler) ListDatastores(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	datastores, err := client.ListDatastores(c.Request.Context())
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, datastores)
}

// GetDatastore returns a specific datastore
func (h *Handler) GetDatastore(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	datastoreID := c.Param("datastore_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	datastore, err := client.GetDatastore(c.Request.Context(), datastoreID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, datastore)
}

// ListNetworks returns a list of networks
func (h *Handler) ListNetworks(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	networks, err := client.ListNetworks(c.Request.Context())
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, networks)
}

// GetNetwork returns a specific network
func (h *Handler) GetNetwork(c *gin.Context) {
	hypervisorID := c.Param("hypervisor_id")
	networkID := c.Param("network_id")

	client, err := h.orchestrator.GetClient(hypervisorID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	network, err := client.GetNetwork(c.Request.Context(), networkID)
	if err != nil {
		h.respondWithError(c, err)
		return
	}

	h.respondWithSuccess(c, network)
}
