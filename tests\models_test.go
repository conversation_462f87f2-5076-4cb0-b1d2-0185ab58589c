package tests

import (
	"testing"
	"time"

	"vm-orchestrator/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestVMState(t *testing.T) {
	tests := []struct {
		name     string
		state    models.VMState
		expected string
	}{
		{"PoweredOn", models.VMStatePoweredOn, "poweredOn"},
		{"PoweredOff", models.VMStatePoweredOff, "poweredOff"},
		{"Suspended", models.VMStateSuspended, "suspended"},
		{"Unknown", models.VMStateUnknown, "unknown"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.state))
		})
	}
}

func TestVirtualMachine(t *testing.T) {
	vm := models.VirtualMachine{
		ID:           "vm-123",
		Name:         "test-vm",
		State:        models.VMStatePoweredOn,
		HypervisorID: "esxi-host-1",
		Hypervisor:   "esxi",
		CPU: models.CPUConfig{
			Cores:   2,
			Sockets: 1,
			Threads: 1,
		},
		Memory: models.MemoryConfig{
			SizeMB: 2048,
		},
		Disks: []models.DiskConfig{
			{
				ID:        "disk0",
				Name:      "disk0",
				SizeGB:    20,
				Datastore: "datastore1",
				Type:      "thin",
			},
		},
		Networks: []models.NetworkConfig{
			{
				ID:      "net0",
				Name:    "net0",
				Network: "VM Network",
				Type:    "vmxnet3",
			},
		},
		Tags:      make(map[string]string),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	assert.Equal(t, "vm-123", vm.ID)
	assert.Equal(t, "test-vm", vm.Name)
	assert.Equal(t, models.VMStatePoweredOn, vm.State)
	assert.Equal(t, "esxi-host-1", vm.HypervisorID)
	assert.Equal(t, "esxi", vm.Hypervisor)
	assert.Equal(t, 2, vm.CPU.Cores)
	assert.Equal(t, 2048, vm.Memory.SizeMB)
	assert.Len(t, vm.Disks, 1)
	assert.Len(t, vm.Networks, 1)
}

func TestVMCreateRequest(t *testing.T) {
	req := models.VMCreateRequest{
		Name:         "test-vm",
		HypervisorID: "esxi-host-1",
		CPU: models.CPUConfig{
			Cores:   2,
			Sockets: 1,
			Threads: 1,
		},
		Memory: models.MemoryConfig{
			SizeMB: 2048,
		},
		Disks: []models.DiskConfig{
			{
				Name:      "disk0",
				SizeGB:    20,
				Datastore: "datastore1",
				Type:      "thin",
			},
		},
		Networks: []models.NetworkConfig{
			{
				Name:    "net0",
				Network: "VM Network",
				Type:    "vmxnet3",
			},
		},
		Tags: map[string]string{
			"environment": "test",
			"project":     "example",
		},
	}

	assert.Equal(t, "test-vm", req.Name)
	assert.Equal(t, "esxi-host-1", req.HypervisorID)
	assert.Equal(t, 2, req.CPU.Cores)
	assert.Equal(t, 2048, req.Memory.SizeMB)
	assert.Len(t, req.Disks, 1)
	assert.Len(t, req.Networks, 1)
	assert.Len(t, req.Tags, 2)
	assert.Equal(t, "test", req.Tags["environment"])
}

func TestAPIError(t *testing.T) {
	err := models.NewAPIError(models.ErrorCodeVMNotFound, "VM not found", "VM ID: vm-123")

	assert.Equal(t, models.ErrorCodeVMNotFound, err.Code)
	assert.Equal(t, "VM not found", err.Message)
	assert.Equal(t, "VM ID: vm-123", err.Details)
	assert.Equal(t, 404, err.HTTPStatusCode())
	assert.Contains(t, err.Error(), "VM_NOT_FOUND")
	assert.Contains(t, err.Error(), "VM not found")
}

func TestAPIErrorHTTPStatusCodes(t *testing.T) {
	tests := []struct {
		name     string
		code     models.ErrorCode
		expected int
	}{
		{"BadRequest", models.ErrorCodeInvalidRequest, 400},
		{"Unauthorized", models.ErrorCodeUnauthorized, 401},
		{"Forbidden", models.ErrorCodeForbidden, 403},
		{"NotFound", models.ErrorCodeNotFound, 404},
		{"Conflict", models.ErrorCodeConflict, 409},
		{"Timeout", models.ErrorCodeTimeout, 408},
		{"InternalError", models.ErrorCodeInternal, 500},
		{"ServiceUnavailable", models.ErrorCodeConnectionFailed, 503},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := models.NewAPIError(tt.code, "test message")
			assert.Equal(t, tt.expected, err.HTTPStatusCode())
		})
	}
}

func TestNewNotFoundError(t *testing.T) {
	err := models.NewNotFoundError("VM", "vm-123")

	assert.Equal(t, models.ErrorCodeNotFound, err.Code)
	assert.Equal(t, "VM not found", err.Message)
	assert.Equal(t, "ID: vm-123", err.Details)
	assert.Equal(t, 404, err.HTTPStatusCode())
}

func TestNewValidationError(t *testing.T) {
	err := models.NewValidationError("Invalid input", "Name is required")

	assert.Equal(t, models.ErrorCodeInvalidRequest, err.Code)
	assert.Equal(t, "Invalid input", err.Message)
	assert.Equal(t, "Name is required", err.Details)
	assert.Equal(t, 400, err.HTTPStatusCode())
}

func TestNewConnectionError(t *testing.T) {
	err := models.NewConnectionError("ESXi", "Connection timeout")

	assert.Equal(t, models.ErrorCodeConnectionFailed, err.Code)
	assert.Equal(t, "Failed to connect to ESXi", err.Message)
	assert.Equal(t, "Connection timeout", err.Details)
	assert.Equal(t, 503, err.HTTPStatusCode())
}

func TestAPIResponse(t *testing.T) {
	// Test success response
	data := map[string]string{"message": "success"}
	successResp := models.NewSuccessResponse(data)

	assert.True(t, successResp.Success)
	assert.Equal(t, data, successResp.Data)
	assert.Nil(t, successResp.Error)

	// Test error response
	apiErr := models.NewAPIError(models.ErrorCodeInternal, "Internal error")
	errorResp := models.NewErrorResponse(apiErr)

	assert.False(t, errorResp.Success)
	assert.Nil(t, errorResp.Data)
	assert.Equal(t, apiErr, errorResp.Error)
}

func TestHostState(t *testing.T) {
	tests := []struct {
		name     string
		state    models.HostState
		expected string
	}{
		{"Connected", models.HostStateConnected, "connected"},
		{"Disconnected", models.HostStateDisconnected, "disconnected"},
		{"Maintenance", models.HostStateMaintenance, "maintenance"},
		{"Unknown", models.HostStateUnknown, "unknown"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.state))
		})
	}
}

func TestSnapshotState(t *testing.T) {
	tests := []struct {
		name     string
		state    models.SnapshotState
		expected string
	}{
		{"Ready", models.SnapshotStateReady, "ready"},
		{"Creating", models.SnapshotStateCreating, "creating"},
		{"Deleting", models.SnapshotStateDeleting, "deleting"},
		{"Error", models.SnapshotStateError, "error"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.state))
		})
	}
}
