package proxmox

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"
)

// ProxmoxVMConfig represents VM configuration from Proxmox API
type ProxmoxVMConfig struct {
	VMID        int    `json:"vmid"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Cores       int    `json:"cores"`
	Sockets     int    `json:"sockets"`
	Memory      int    `json:"memory"`
	Boot        string `json:"boot"`
	SCSI0       string `json:"scsi0"`
	Net0        string `json:"net0"`
	IDE2        string `json:"ide2"`
	OSType      string `json:"ostype"`
}

// ProxmoxVMStatus represents VM status from Proxmox API
type ProxmoxVMStatus struct {
	VMID    int     `json:"vmid"`
	Status  string  `json:"status"`
	CPU     float64 `json:"cpu"`
	Mem     int64   `json:"mem"`
	MaxMem  int64   `json:"maxmem"`
	Disk    int64   `json:"disk"`
	MaxDisk int64   `json:"maxdisk"`
	NetIn   int64   `json:"netin"`
	NetOut  int64   `json:"netout"`
	Uptime  int64   `json:"uptime"`
}

// convertProxmoxVMToModel converts a basic Proxmox VM to our VM model
func (c *Client) convertProxmoxVMToModel(vm ProxmoxVM, node string) models.VirtualMachine {
	state := c.convertProxmoxState(vm.Status)

	return models.VirtualMachine{
		ID:           strconv.Itoa(vm.VMID),
		Name:         vm.Name,
		State:        state,
		HypervisorID: c.config.ID,
		Hypervisor:   "proxmox",
		CPU: models.CPUConfig{
			Cores:   vm.CPU,
			Sockets: 1, // Default, would need config API to get actual value
			Threads: 1,
		},
		Memory: models.MemoryConfig{
			SizeMB: vm.Memory / 1024 / 1024, // Convert bytes to MB
		},
		Disks:     []models.DiskConfig{},    // Would need config API to populate
		Networks:  []models.NetworkConfig{}, // Would need config API to populate
		Tags:      make(map[string]string),
		CreatedAt: time.Now(), // Would need to get actual creation time
		UpdatedAt: time.Now(),
	}
}

// convertProxmoxVMConfigToModel converts detailed Proxmox VM config to our VM model
func (c *Client) convertProxmoxVMConfigToModel(config ProxmoxVMConfig, status ProxmoxVMStatus, node string) *models.VirtualMachine {
	state := c.convertProxmoxState(status.Status)

	// Parse disk configuration
	var disks []models.DiskConfig
	if config.SCSI0 != "" {
		disk := c.parseDiskConfig(config.SCSI0, "scsi0")
		if disk != nil {
			disks = append(disks, *disk)
		}
	}

	// Parse network configuration
	var networks []models.NetworkConfig
	if config.Net0 != "" {
		network := c.parseNetworkConfig(config.Net0, "net0")
		if network != nil {
			networks = append(networks, *network)
		}
	}

	return &models.VirtualMachine{
		ID:           strconv.Itoa(config.VMID),
		Name:         config.Name,
		State:        state,
		HypervisorID: c.config.ID,
		Hypervisor:   "proxmox",
		CPU: models.CPUConfig{
			Cores:   config.Cores,
			Sockets: config.Sockets,
			Threads: 1,
		},
		Memory: models.MemoryConfig{
			SizeMB: config.Memory,
		},
		Disks:     disks,
		Networks:  networks,
		Tags:      make(map[string]string),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// convertProxmoxState converts Proxmox VM state to our model
func (c *Client) convertProxmoxState(state string) models.VMState {
	switch state {
	case "running":
		return models.VMStatePoweredOn
	case "stopped":
		return models.VMStatePoweredOff
	case "suspended":
		return models.VMStateSuspended
	default:
		return models.VMStateUnknown
	}
}

// parseDiskConfig parses Proxmox disk configuration string
func (c *Client) parseDiskConfig(diskStr, diskID string) *models.DiskConfig {
	// Example: "local-lvm:vm-100-disk-0,size=32G"
	parts := strings.Split(diskStr, ",")
	if len(parts) == 0 {
		return nil
	}

	// Extract storage and disk name
	storageParts := strings.Split(parts[0], ":")
	if len(storageParts) != 2 {
		return nil
	}

	storage := storageParts[0]
	diskName := storageParts[1]

	// Extract size
	sizeGB := 0
	for _, part := range parts[1:] {
		if strings.HasPrefix(part, "size=") {
			sizeStr := strings.TrimPrefix(part, "size=")
			sizeStr = strings.TrimSuffix(sizeStr, "G")
			if size, err := strconv.Atoi(sizeStr); err == nil {
				sizeGB = size
			}
		}
	}

	return &models.DiskConfig{
		ID:        diskID,
		Name:      diskName,
		SizeGB:    sizeGB,
		Datastore: storage,
		Type:      "thin", // Default for Proxmox
	}
}

// parseNetworkConfig parses Proxmox network configuration string
func (c *Client) parseNetworkConfig(netStr, netID string) *models.NetworkConfig {
	// Example: "virtio=52:54:00:12:34:56,bridge=vmbr0"
	parts := strings.Split(netStr, ",")
	if len(parts) == 0 {
		return nil
	}

	// Extract MAC and type
	macParts := strings.Split(parts[0], "=")
	if len(macParts) != 2 {
		return nil
	}

	netType := macParts[0]
	mac := macParts[1]

	// Extract bridge
	bridge := ""
	for _, part := range parts[1:] {
		if strings.HasPrefix(part, "bridge=") {
			bridge = strings.TrimPrefix(part, "bridge=")
		}
	}

	return &models.NetworkConfig{
		ID:      netID,
		Name:    netID,
		Network: bridge,
		MAC:     mac,
		Type:    netType,
	}
}

// matchesFilters checks if a VM matches the given filters
func (c *Client) matchesFilters(vm *models.VirtualMachine, filters interfaces.VMFilters) bool {
	// Name filter
	if filters.Name != "" && !strings.Contains(strings.ToLower(vm.Name), strings.ToLower(filters.Name)) {
		return false
	}

	// State filter
	if filters.State != "" && vm.State != filters.State {
		return false
	}

	// Hypervisor ID filter
	if filters.HypervisorID != "" && vm.HypervisorID != filters.HypervisorID {
		return false
	}

	// Tags filter
	if len(filters.Tags) > 0 {
		for key, value := range filters.Tags {
			if vmValue, exists := vm.Tags[key]; !exists || vmValue != value {
				return false
			}
		}
	}

	return true
}

// findVMNode finds which node contains a specific VM
func (c *Client) findVMNode(ctx context.Context, vmid int) (string, error) {
	nodes, err := c.getNodes(ctx)
	if err != nil {
		return "", err
	}

	for _, node := range nodes {
		resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/config", node, vmid), nil)
		if err != nil {
			continue
		}

		if resp.StatusCode == 200 {
			resp.Body.Close()
			return node, nil
		}
		resp.Body.Close()
	}

	return "", models.NewNotFoundError("VM", strconv.Itoa(vmid))
}

// getNextVMID gets the next available VM ID
func (c *Client) getNextVMID(ctx context.Context) (int, error) {
	resp, err := c.makeRequest(ctx, "GET", "/cluster/nextid", nil)
	if err != nil {
		return 0, err
	}

	var result struct {
		Data int `json:"data"`
	}

	if err := c.parseResponse(resp, &result.Data); err != nil {
		return 0, err
	}

	return result.Data, nil
}

// buildVMConfig builds VM configuration for creation
func (c *Client) buildVMConfig(req models.VMCreateRequest, vmid int) url.Values {
	config := url.Values{}
	
	config.Set("vmid", strconv.Itoa(vmid))
	config.Set("name", req.Name)
	config.Set("cores", strconv.Itoa(req.CPU.Cores))
	config.Set("sockets", strconv.Itoa(req.CPU.Sockets))
	config.Set("memory", strconv.Itoa(req.Memory.SizeMB))
	config.Set("ostype", "l26") // Default to Linux

	// Configure disks
	if len(req.Disks) > 0 {
		disk := req.Disks[0]
		diskConfig := fmt.Sprintf("%s:vm-%d-disk-0,size=%dG", disk.Datastore, vmid, disk.SizeGB)
		config.Set("scsi0", diskConfig)
	}

	// Configure networks
	if len(req.Networks) > 0 {
		network := req.Networks[0]
		netConfig := fmt.Sprintf("virtio,bridge=%s", network.Network)
		config.Set("net0", netConfig)
	}

	return config
}

// ListHosts returns a list of Proxmox nodes
func (c *Client) ListHosts(ctx context.Context) ([]models.Host, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	resp, err := c.makeRequest(ctx, "GET", "/nodes", nil)
	if err != nil {
		return nil, err
	}

	var nodes []ProxmoxNode
	if err := c.parseResponse(resp, &nodes); err != nil {
		return nil, err
	}

	var hosts []models.Host
	for _, node := range nodes {
		host := c.convertProxmoxNodeToHost(node)
		hosts = append(hosts, host)
	}

	return hosts, nil
}

// GetHost returns a specific host
func (c *Client) GetHost(ctx context.Context, hostID string) (*models.Host, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/status", hostID), nil)
	if err != nil {
		return nil, models.NewNotFoundError("Host", hostID)
	}

	var nodeStatus ProxmoxNodeStatus
	if err := c.parseResponse(resp, &nodeStatus); err != nil {
		return nil, err
	}

	return c.convertProxmoxNodeStatusToHost(nodeStatus), nil
}

// ProxmoxNode represents a Proxmox node
type ProxmoxNode struct {
	Node   string  `json:"node"`
	Status string  `json:"status"`
	CPU    float64 `json:"cpu"`
	MaxCPU int     `json:"maxcpu"`
	Mem    int64   `json:"mem"`
	MaxMem int64   `json:"maxmem"`
	Uptime int64   `json:"uptime"`
}

// ProxmoxNodeStatus represents detailed node status
type ProxmoxNodeStatus struct {
	Node    string  `json:"node"`
	Status  string  `json:"status"`
	CPU     float64 `json:"cpu"`
	MaxCPU  int     `json:"maxcpu"`
	Mem     int64   `json:"mem"`
	MaxMem  int64   `json:"maxmem"`
	Uptime  int64   `json:"uptime"`
	LoadAvg []float64 `json:"loadavg"`
}

// convertProxmoxNodeToHost converts a Proxmox node to our host model
func (c *Client) convertProxmoxNodeToHost(node ProxmoxNode) models.Host {
	state := models.HostStateConnected
	if node.Status != "online" {
		state = models.HostStateDisconnected
	}

	return models.Host{
		ID:      node.Node,
		Name:    node.Node,
		Type:    "proxmox",
		Address: c.config.Host, // Simplified - would need actual node IP
		State:   state,
		Version: "", // Would need additional API call
		CPU: models.HostCPUInfo{
			Cores:        node.MaxCPU,
			UsagePercent: node.CPU * 100,
		},
		Memory: models.HostMemoryInfo{
			TotalMB:      int(node.MaxMem / 1024 / 1024),
			UsedMB:       int(node.Mem / 1024 / 1024),
			FreeMB:       int((node.MaxMem - node.Mem) / 1024 / 1024),
			UsagePercent: float64(node.Mem) / float64(node.MaxMem) * 100,
		},
		Storage:   []models.HostStorageInfo{}, // Would need additional API calls
		Networks:  []models.HostNetworkInfo{}, // Would need additional API calls
		VMCount:   0,                          // Would need additional API calls
		Tags:      make(map[string]string),
		LastSeen:  time.Now(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// convertProxmoxNodeStatusToHost converts detailed node status to our host model
func (c *Client) convertProxmoxNodeStatusToHost(status ProxmoxNodeStatus) *models.Host {
	state := models.HostStateConnected
	if status.Status != "online" {
		state = models.HostStateDisconnected
	}

	return &models.Host{
		ID:      status.Node,
		Name:    status.Node,
		Type:    "proxmox",
		Address: c.config.Host,
		State:   state,
		Version: "",
		CPU: models.HostCPUInfo{
			Cores:        status.MaxCPU,
			UsagePercent: status.CPU * 100,
		},
		Memory: models.HostMemoryInfo{
			TotalMB:      int(status.MaxMem / 1024 / 1024),
			UsedMB:       int(status.Mem / 1024 / 1024),
			FreeMB:       int((status.MaxMem - status.Mem) / 1024 / 1024),
			UsagePercent: float64(status.Mem) / float64(status.MaxMem) * 100,
		},
		Storage:   []models.HostStorageInfo{},
		Networks:  []models.HostNetworkInfo{},
		VMCount:   0,
		Tags:      make(map[string]string),
		LastSeen:  time.Now(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// GetHostMetrics returns performance metrics for a host
func (c *Client) GetHostMetrics(ctx context.Context, hostID string) (*models.HostMetrics, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/status", hostID), nil)
	if err != nil {
		return nil, models.NewNotFoundError("Host", hostID)
	}

	var status ProxmoxNodeStatus
	if err := c.parseResponse(resp, &status); err != nil {
		return nil, err
	}

	metrics := &models.HostMetrics{
		HostID:    hostID,
		Timestamp: time.Now(),
		CPU: models.CPUMetrics{
			UsagePercent: status.CPU * 100,
		},
		Memory: models.MemoryMetrics{
			UsagePercent: float64(status.Mem) / float64(status.MaxMem) * 100,
			UsageMB:      float64(status.Mem / 1024 / 1024),
			TotalMB:      float64(status.MaxMem / 1024 / 1024),
		},
	}

	return metrics, nil
}
