package proxmox

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"
)

// GetVM returns a specific virtual machine
func (c *Client) GetVM(ctx context.Context, vmID string) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return nil, models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Get VM details
	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/config", node, id), nil)
	if err != nil {
		return nil, err
	}

	var vmConfig ProxmoxVMConfig
	if err := c.parseResponse(resp, &vmConfig); err != nil {
		return nil, err
	}

	// Get VM status
	resp, err = c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/status/current", node, id), nil)
	if err != nil {
		return nil, err
	}

	var vmStatus ProxmoxVMStatus
	if err := c.parseResponse(resp, &vmStatus); err != nil {
		return nil, err
	}

	return c.convertProxmoxVMConfigToModel(vmConfig, vmStatus, node), nil
}

// CreateVM creates a new virtual machine
func (c *Client) CreateVM(ctx context.Context, req models.VMCreateRequest) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Find a suitable node (use first available node for simplicity)
	nodes, err := c.getNodes(ctx)
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "No nodes available", "")
	}

	node := nodes[0] // Use first node

	// Get next available VM ID
	vmid, err := c.getNextVMID(ctx)
	if err != nil {
		return nil, err
	}

	// Build VM configuration
	config := c.buildVMConfig(req, vmid)

	// Create VM
	resp, err := c.makeRequest(ctx, "POST", fmt.Sprintf("/nodes/%s/qemu", node), config)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to create VM", err.Error())
	}

	if err := c.parseResponse(resp, nil); err != nil {
		return nil, err
	}

	// Wait a moment for VM to be created
	time.Sleep(2 * time.Second)

	// Return the created VM
	return c.GetVM(ctx, strconv.Itoa(vmid))
}

// UpdateVM updates an existing virtual machine
func (c *Client) UpdateVM(ctx context.Context, vmID string, req models.VMUpdateRequest) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return nil, models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Build update configuration
	config := url.Values{}
	
	if req.Name != nil {
		config.Set("name", *req.Name)
	}
	
	if req.CPU != nil {
		config.Set("cores", strconv.Itoa(req.CPU.Cores))
		config.Set("sockets", strconv.Itoa(req.CPU.Sockets))
	}
	
	if req.Memory != nil {
		config.Set("memory", strconv.Itoa(req.Memory.SizeMB))
	}

	// Update VM
	resp, err := c.makeRequest(ctx, "PUT", fmt.Sprintf("/nodes/%s/qemu/%d/config", node, id), config)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to update VM", err.Error())
	}

	if err := c.parseResponse(resp, nil); err != nil {
		return nil, err
	}

	return c.GetVM(ctx, vmID)
}

// DeleteVM deletes a virtual machine
func (c *Client) DeleteVM(ctx context.Context, vmID string, force bool) error {
	if !c.connected {
		return models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return err
	}

	// Check VM status
	if !force {
		resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/status/current", node, id), nil)
		if err != nil {
			return err
		}

		var status ProxmoxVMStatus
		if err := c.parseResponse(resp, &status); err != nil {
			return err
		}

		if status.Status == "running" {
			return models.NewAPIError(models.ErrorCodeVMInvalidState, "VM is running", "Use force=true to stop and delete")
		}
	} else {
		// Stop VM if running
		c.StopVM(ctx, vmID, true)
	}

	// Delete VM
	resp, err := c.makeRequest(ctx, "DELETE", fmt.Sprintf("/nodes/%s/qemu/%d", node, id), nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to delete VM", err.Error())
	}

	return c.parseResponse(resp, nil)
}

// StartVM powers on a virtual machine
func (c *Client) StartVM(ctx context.Context, vmID string) error {
	return c.changeVMState(ctx, vmID, "start")
}

// StopVM powers off a virtual machine
func (c *Client) StopVM(ctx context.Context, vmID string, force bool) error {
	action := "shutdown"
	if force {
		action = "stop"
	}
	return c.changeVMState(ctx, vmID, action)
}

// RestartVM restarts a virtual machine
func (c *Client) RestartVM(ctx context.Context, vmID string, force bool) error {
	action := "reboot"
	if force {
		action = "reset"
	}
	return c.changeVMState(ctx, vmID, action)
}

// SuspendVM suspends a virtual machine
func (c *Client) SuspendVM(ctx context.Context, vmID string) error {
	return c.changeVMState(ctx, vmID, "suspend")
}

// ResumeVM resumes a suspended virtual machine
func (c *Client) ResumeVM(ctx context.Context, vmID string) error {
	return c.changeVMState(ctx, vmID, "resume")
}

// changeVMState changes the power state of a VM
func (c *Client) changeVMState(ctx context.Context, vmID, action string) error {
	if !c.connected {
		return models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return err
	}

	// Execute action
	resp, err := c.makeRequest(ctx, "POST", fmt.Sprintf("/nodes/%s/qemu/%d/status/%s", node, id, action), nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, fmt.Sprintf("Failed to %s VM", action), err.Error())
	}

	return c.parseResponse(resp, nil)
}

// GetVMMetrics returns performance metrics for a VM
func (c *Client) GetVMMetrics(ctx context.Context, vmID string) (*models.VMMetrics, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return nil, models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Get VM status with metrics
	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/status/current", node, id), nil)
	if err != nil {
		return nil, err
	}

	var status ProxmoxVMStatus
	if err := c.parseResponse(resp, &status); err != nil {
		return nil, err
	}

	metrics := &models.VMMetrics{
		VMID:      vmID,
		Timestamp: time.Now(),
		CPU: models.CPUMetrics{
			UsagePercent: status.CPU * 100, // Proxmox returns CPU as decimal
		},
		Memory: models.MemoryMetrics{
			UsageMB:      float64(status.Mem / 1024 / 1024),
			TotalMB:      float64(status.MaxMem / 1024 / 1024),
			UsagePercent: float64(status.Mem) / float64(status.MaxMem) * 100,
		},
		Disk: models.DiskMetrics{
			UsagePercent: float64(status.Disk) / float64(status.MaxDisk) * 100,
		},
		Network: models.NetworkMetrics{
			RxMBps: float64(status.NetIn) / 1024 / 1024,
			TxMBps: float64(status.NetOut) / 1024 / 1024,
		},
	}

	return metrics, nil
}

// GetVMConsoleURL returns console URL for a VM
func (c *Client) GetVMConsoleURL(ctx context.Context, vmID string) (string, error) {
	if !c.connected {
		return "", models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return "", models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return "", err
	}

	// Generate console URL (simplified - in real implementation would create proper console session)
	return fmt.Sprintf("https://%s:8006/#v1:0:=qemu/%d:4::::::", c.config.Host, id), nil
}
