package proxmox

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"vm-orchestrator/internal/models"
)

// ListSnapshots returns a list of snapshots for a VM
func (c *Client) ListSnapshots(ctx context.Context, vmID string) ([]models.Snapshot, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return nil, models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Get snapshots
	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/snapshot", node, id), nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list snapshots", err.Error())
	}

	var proxmoxSnapshots []ProxmoxSnapshot
	if err := c.parseResponse(resp, &proxmoxSnapshots); err != nil {
		return nil, err
	}

	// Convert to our snapshot model
	var snapshots []models.Snapshot
	for _, snap := range proxmoxSnapshots {
		// Skip the "current" snapshot which is not a real snapshot
		if snap.Name == "current" {
			continue
		}

		snapshot := models.Snapshot{
			ID:           snap.Name,
			Name:         snap.Name,
			Description:  snap.Description,
			VMID:         vmID,
			VMName:       "", // Would need additional API call to get VM name
			HypervisorID: c.config.ID,
			Hypervisor:   "proxmox",
			State:        models.SnapshotStateReady,
			SizeGB:       0, // Proxmox doesn't provide snapshot size in list
			ParentID:     snap.Parent,
			CreatedAt:    time.Unix(snap.SnapTime, 0),
			UpdatedAt:    time.Unix(snap.SnapTime, 0),
		}

		snapshots = append(snapshots, snapshot)
	}

	return snapshots, nil
}

// GetSnapshot returns a specific snapshot
func (c *Client) GetSnapshot(ctx context.Context, snapshotID string) (*models.Snapshot, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Find VM that contains this snapshot
	// This is a simplified implementation - in practice, you'd need to search all VMs
	// or maintain a mapping of snapshot IDs to VM IDs
	return nil, models.NewAPIError(models.ErrorCodeNotFound, "Snapshot lookup by ID not implemented", "Use ListSnapshots with VM ID instead")
}

// CreateSnapshot creates a new snapshot
func (c *Client) CreateSnapshot(ctx context.Context, vmID string, req models.SnapshotCreateRequest) (*models.Snapshot, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return nil, models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Create snapshot
	snapData := url.Values{
		"snapname":    {req.Name},
		"description": {req.Description},
	}

	if req.Memory {
		snapData.Set("vmstate", "1")
	}

	resp, err := c.makeRequest(ctx, "POST", fmt.Sprintf("/nodes/%s/qemu/%d/snapshot", node, id), snapData)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Failed to create snapshot", err.Error())
	}

	if err := c.parseResponse(resp, nil); err != nil {
		return nil, err
	}

	// Return created snapshot
	snapshot := &models.Snapshot{
		ID:           req.Name,
		Name:         req.Name,
		Description:  req.Description,
		VMID:         vmID,
		VMName:       "", // Would need additional API call
		HypervisorID: c.config.ID,
		Hypervisor:   "proxmox",
		State:        models.SnapshotStateReady,
		SizeGB:       0,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	return snapshot, nil
}

// RestoreSnapshot restores a VM to a snapshot
func (c *Client) RestoreSnapshot(ctx context.Context, snapshotID string, req models.SnapshotRestoreRequest) error {
	if !c.connected {
		return models.NewConnectionError("Proxmox", "Not connected")
	}

	// This is a simplified implementation
	// In practice, you'd need to find the VM that contains this snapshot
	return models.NewAPIError(models.ErrorCodeNotFound, "Snapshot restore by ID not implemented", "Use VM ID and snapshot name instead")
}

// DeleteSnapshot deletes a snapshot
func (c *Client) DeleteSnapshot(ctx context.Context, snapshotID string) error {
	if !c.connected {
		return models.NewConnectionError("Proxmox", "Not connected")
	}

	// This is a simplified implementation
	// In practice, you'd need to find the VM that contains this snapshot
	return models.NewAPIError(models.ErrorCodeNotFound, "Snapshot deletion by ID not implemented", "Use VM ID and snapshot name instead")
}

// GetSnapshotTree returns the snapshot tree for a VM
func (c *Client) GetSnapshotTree(ctx context.Context, vmID string) (*models.SnapshotTree, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Get all snapshots for the VM
	snapshots, err := c.ListSnapshots(ctx, vmID)
	if err != nil {
		return nil, err
	}

	if len(snapshots) == 0 {
		return &models.SnapshotTree{}, nil
	}

	// Build tree structure (simplified - assumes linear snapshot chain)
	// In practice, you'd need to build a proper tree based on parent relationships
	tree := &models.SnapshotTree{
		Snapshot: &snapshots[0],
	}

	// Add children
	for i := 1; i < len(snapshots); i++ {
		childTree := &models.SnapshotTree{
			Snapshot: &snapshots[i],
		}
		tree.Children = append(tree.Children, childTree)
	}

	return tree, nil
}

// RestoreVMSnapshot restores a VM to a specific snapshot (helper method with VM ID)
func (c *Client) RestoreVMSnapshot(ctx context.Context, vmID, snapshotName string, req models.SnapshotRestoreRequest) error {
	if !c.connected {
		return models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return err
	}

	// Power off VM if requested
	if req.PowerOff {
		c.StopVM(ctx, vmID, true)
		time.Sleep(2 * time.Second)
	}

	// Restore snapshot
	resp, err := c.makeRequest(ctx, "POST", fmt.Sprintf("/nodes/%s/qemu/%d/snapshot/%s/rollback", node, id, snapshotName), nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Failed to restore snapshot", err.Error())
	}

	return c.parseResponse(resp, nil)
}

// DeleteVMSnapshot deletes a specific snapshot (helper method with VM ID)
func (c *Client) DeleteVMSnapshot(ctx context.Context, vmID, snapshotName string) error {
	if !c.connected {
		return models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse VM ID
	id, err := strconv.Atoi(vmID)
	if err != nil {
		return models.NewValidationError("Invalid VM ID", "VM ID must be numeric")
	}

	// Find the node containing this VM
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return err
	}

	// Delete snapshot
	resp, err := c.makeRequest(ctx, "DELETE", fmt.Sprintf("/nodes/%s/qemu/%d/snapshot/%s", node, id, snapshotName), nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeSnapshotOperationFailed, "Failed to delete snapshot", err.Error())
	}

	return c.parseResponse(resp, nil)
}

// ProxmoxSnapshot represents a snapshot as returned by Proxmox API
type ProxmoxSnapshot struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	SnapTime    int64  `json:"snaptime"`
	Parent      string `json:"parent"`
	VMState     int    `json:"vmstate"`
}
