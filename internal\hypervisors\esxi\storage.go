package esxi

import (
	"context"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"

	"github.com/vmware/govmomi/object"
)

// ListDatastores returns a list of datastores
func (c *Client) ListDatastores(ctx context.Context) ([]interfaces.DatastoreInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	datastores, err := c.finder.DatastoreList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list datastores", err.Error())
	}

	var result []interfaces.DatastoreInfo
	for _, ds := range datastores {
		dsInfo, err := c.convertDatastoreToInfo(ctx, ds)
		if err != nil {
			continue // Skip datastores that can't be converted
		}
		result = append(result, *dsInfo)
	}

	return result, nil
}

// GetDatastore returns a specific datastore
func (c *Client) GetDatastore(ctx context.Context, datastoreID string) (*interfaces.DatastoreInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	ds, err := c.finder.Datastore(ctx, datastoreID)
	if err != nil {
		return nil, models.NewNotFoundError("Datastore", datastoreID)
	}

	return c.convertDatastoreToInfo(ctx, ds)
}

// convertDatastoreToInfo converts a govmomi datastore to our datastore info model
func (c *Client) convertDatastoreToInfo(ctx context.Context, ds *object.Datastore) (*interfaces.DatastoreInfo, error) {
	var dsObj object.Datastore
	err := ds.Properties(ctx, ds.Reference(), []string{"summary", "info"}, &dsObj)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get datastore properties", err.Error())
	}

	summary := dsObj.Summary
	info := dsObj.Info

	// Calculate usage
	totalGB := int(summary.Capacity / 1024 / 1024 / 1024)
	freeGB := int(summary.FreeSpace / 1024 / 1024 / 1024)
	usedGB := totalGB - freeGB
	usagePercent := float64(0)
	if totalGB > 0 {
		usagePercent = float64(usedGB) / float64(totalGB) * 100
	}

	dsInfo := &interfaces.DatastoreInfo{
		ID:           ds.Reference().Value,
		Name:         summary.Name,
		Type:         summary.Type,
		TotalGB:      totalGB,
		FreeGB:       freeGB,
		UsedGB:       usedGB,
		UsagePercent: usagePercent,
		Accessible:   summary.Accessible,
	}

	return dsInfo, nil
}

// ListNetworks returns a list of networks
func (c *Client) ListNetworks(ctx context.Context) ([]interfaces.NetworkInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	networks, err := c.finder.NetworkList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list networks", err.Error())
	}

	var result []interfaces.NetworkInfo
	for _, network := range networks {
		networkInfo, err := c.convertNetworkToInfo(ctx, network)
		if err != nil {
			continue // Skip networks that can't be converted
		}
		result = append(result, *networkInfo)
	}

	return result, nil
}

// GetNetwork returns a specific network
func (c *Client) GetNetwork(ctx context.Context, networkID string) (*interfaces.NetworkInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	network, err := c.finder.Network(ctx, networkID)
	if err != nil {
		return nil, models.NewNotFoundError("Network", networkID)
	}

	return c.convertNetworkToInfo(ctx, network)
}

// convertNetworkToInfo converts a govmomi network to our network info model
func (c *Client) convertNetworkToInfo(ctx context.Context, network object.NetworkReference) (*interfaces.NetworkInfo, error) {
	var networkObj object.Network
	err := network.Properties(ctx, network.Reference(), []string{"summary", "name"}, &networkObj)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get network properties", err.Error())
	}

	networkInfo := &interfaces.NetworkInfo{
		ID:     network.Reference().Value,
		Name:   networkObj.Name,
		Type:   "standard", // Default type, would need more logic to determine actual type
		Active: true,       // Assume active, would need more logic to determine actual status
	}

	// Try to extract VLAN information if it's a distributed port group
	if dvpg, ok := network.(*object.DistributedVirtualPortgroup); ok {
		var dvpgObj object.DistributedVirtualPortgroup
		err := dvpg.Properties(ctx, dvpg.Reference(), []string{"config"}, &dvpgObj)
		if err == nil && dvpgObj.Config != nil {
			networkInfo.Type = "distributed"
			// Extract VLAN info from config if available
			// This would require more complex logic to parse the VLAN configuration
		}
	}

	return networkInfo, nil
}
