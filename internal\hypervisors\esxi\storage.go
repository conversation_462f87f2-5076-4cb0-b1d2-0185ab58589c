package esxi

import (
	"context"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/types"
)

// ListDatastores returns a list of datastores
func (c *Client) ListDatastores(ctx context.Context) ([]interfaces.DatastoreInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	datastores, err := c.finder.DatastoreList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list datastores", err.Error())
	}

	var result []interfaces.DatastoreInfo
	for _, ds := range datastores {
		dsInfo, err := c.convertDatastoreToInfo(ctx, ds)
		if err != nil {
			continue // Skip datastores that can't be converted
		}
		result = append(result, *dsInfo)
	}

	return result, nil
}

// GetDatastore returns a specific datastore
func (c *Client) GetDatastore(ctx context.Context, datastoreID string) (*interfaces.DatastoreInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	ds, err := c.finder.Datastore(ctx, datastoreID)
	if err != nil {
		return nil, models.NewNotFoundError("Datastore", datastoreID)
	}

	return c.convertDatastoreToInfo(ctx, ds)
}

// convertDatastoreToInfo converts a govmomi datastore to our datastore info model
func (c *Client) convertDatastoreToInfo(ctx context.Context, ds *object.Datastore) (*interfaces.DatastoreInfo, error) {
	var dsProps struct {
		Summary *types.DatastoreSummary `mo:"summary"`
	}
	err := ds.Properties(ctx, ds.Reference(), []string{"summary"}, &dsProps)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get datastore properties", err.Error())
	}

	summary := dsProps.Summary

	// Calculate usage
	totalGB := int(summary.Capacity / 1024 / 1024 / 1024)
	freeGB := int(summary.FreeSpace / 1024 / 1024 / 1024)
	usedGB := totalGB - freeGB
	usagePercent := float64(0)
	if totalGB > 0 {
		usagePercent = float64(usedGB) / float64(totalGB) * 100
	}

	dsInfo := &interfaces.DatastoreInfo{
		ID:           ds.Reference().Value,
		Name:         summary.Name,
		Type:         summary.Type,
		TotalGB:      totalGB,
		FreeGB:       freeGB,
		UsedGB:       usedGB,
		UsagePercent: usagePercent,
		Accessible:   summary.Accessible,
	}

	return dsInfo, nil
}

// ListNetworks returns a list of networks
func (c *Client) ListNetworks(ctx context.Context) ([]interfaces.NetworkInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	networks, err := c.finder.NetworkList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list networks", err.Error())
	}

	var result []interfaces.NetworkInfo
	for _, network := range networks {
		networkInfo, err := c.convertNetworkToInfo(ctx, network)
		if err != nil {
			continue // Skip networks that can't be converted
		}
		result = append(result, *networkInfo)
	}

	return result, nil
}

// GetNetwork returns a specific network
func (c *Client) GetNetwork(ctx context.Context, networkID string) (*interfaces.NetworkInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	network, err := c.finder.Network(ctx, networkID)
	if err != nil {
		return nil, models.NewNotFoundError("Network", networkID)
	}

	return c.convertNetworkToInfo(ctx, network)
}

// convertNetworkToInfo converts a govmomi network to our network info model
func (c *Client) convertNetworkToInfo(ctx context.Context, network object.NetworkReference) (*interfaces.NetworkInfo, error) {
	// Get network name from reference
	networkName := network.Reference().Value

	networkInfo := &interfaces.NetworkInfo{
		ID:     network.Reference().Value,
		Name:   networkName,
		Type:   "standard", // Default type, would need more logic to determine actual type
		Active: true,       // Assume active, would need more logic to determine actual status
	}

	// Try to extract VLAN information if it's a distributed port group
	if dvpg, ok := network.(*object.DistributedVirtualPortgroup); ok {
		var dvpgProps struct {
			Name string `mo:"name"`
		}
		err := dvpg.Properties(ctx, dvpg.Reference(), []string{"name"}, &dvpgProps)
		if err == nil {
			networkInfo.Type = "distributed"
			networkInfo.Name = dvpgProps.Name
		}
	}

	return networkInfo, nil
}
