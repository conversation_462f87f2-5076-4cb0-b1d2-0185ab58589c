package logging

import (
	"io"
	"os"
	"time"

	"vm-orchestrator/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// NewLogger creates a new logger instance based on configuration
func NewLogger(cfg config.LoggingConfig) *logrus.Logger {
	logger := logrus.New()

	// Set log level
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// Set log format
	switch cfg.Format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: time.RFC3339,
		})
	default:
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	}

	// Set output
	var output io.Writer
	switch cfg.Output {
	case "stdout":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// Try to open file
		if file, err := os.OpenFile(cfg.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
			output = file
		} else {
			// Fallback to stdout
			output = os.Stdout
			logger.WithError(err).Warn("Failed to open log file, falling back to stdout")
		}
	}
	logger.SetOutput(output)

	return logger
}

// GinLogger returns a gin middleware for logging HTTP requests
func GinLogger(logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get client IP
		clientIP := c.ClientIP()

		// Get status code
		statusCode := c.Writer.Status()

		// Get response size
		bodySize := c.Writer.Size()

		// Build log entry
		entry := logger.WithFields(logrus.Fields{
			"status":     statusCode,
			"latency":    latency,
			"client_ip":  clientIP,
			"method":     c.Request.Method,
			"path":       path,
			"query":      raw,
			"user_agent": c.Request.UserAgent(),
			"body_size":  bodySize,
		})

		// Add error if present
		if len(c.Errors) > 0 {
			entry = entry.WithField("errors", c.Errors.String())
		}

		// Log based on status code
		switch {
		case statusCode >= 500:
			entry.Error("Server error")
		case statusCode >= 400:
			entry.Warn("Client error")
		case statusCode >= 300:
			entry.Info("Redirection")
		default:
			entry.Info("Request completed")
		}
	}
}

// RequestIDMiddleware adds a unique request ID to each request
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := generateRequestID()
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	// Simple implementation - in production, use a proper UUID library
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString generates a random string of specified length
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// ContextLogger returns a logger with context fields
func ContextLogger(logger *logrus.Logger, c *gin.Context) *logrus.Entry {
	entry := logger.WithFields(logrus.Fields{
		"method": c.Request.Method,
		"path":   c.Request.URL.Path,
	})

	if requestID, exists := c.Get("request_id"); exists {
		entry = entry.WithField("request_id", requestID)
	}

	return entry
}

// HypervisorLogger returns a logger with hypervisor context
func HypervisorLogger(logger *logrus.Logger, hypervisorID, hypervisorType string) *logrus.Entry {
	return logger.WithFields(logrus.Fields{
		"hypervisor_id":   hypervisorID,
		"hypervisor_type": hypervisorType,
	})
}

// VMLogger returns a logger with VM context
func VMLogger(logger *logrus.Logger, hypervisorID, vmID string) *logrus.Entry {
	return logger.WithFields(logrus.Fields{
		"hypervisor_id": hypervisorID,
		"vm_id":         vmID,
	})
}

// OperationLogger returns a logger with operation context
func OperationLogger(logger *logrus.Logger, operation string) *logrus.Entry {
	return logger.WithFields(logrus.Fields{
		"operation": operation,
		"timestamp": time.Now().UTC(),
	})
}

// ErrorLogger logs errors with additional context
func ErrorLogger(logger *logrus.Logger, err error, context map[string]interface{}) {
	entry := logger.WithError(err)
	
	for key, value := range context {
		entry = entry.WithField(key, value)
	}
	
	entry.Error("Operation failed")
}

// AuditLogger logs audit events
func AuditLogger(logger *logrus.Logger, event string, user string, details map[string]interface{}) {
	entry := logger.WithFields(logrus.Fields{
		"event":     event,
		"user":      user,
		"timestamp": time.Now().UTC(),
		"audit":     true,
	})
	
	for key, value := range details {
		entry = entry.WithField(key, value)
	}
	
	entry.Info("Audit event")
}

// PerformanceLogger logs performance metrics
func PerformanceLogger(logger *logrus.Logger, operation string, duration time.Duration, details map[string]interface{}) {
	entry := logger.WithFields(logrus.Fields{
		"operation":   operation,
		"duration_ms": duration.Milliseconds(),
		"performance": true,
	})
	
	for key, value := range details {
		entry = entry.WithField(key, value)
	}
	
	entry.Info("Performance metric")
}
