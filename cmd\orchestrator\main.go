package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"vm-orchestrator/internal/api"
	"vm-orchestrator/internal/config"
	"vm-orchestrator/internal/logging"
	"vm-orchestrator/internal/orchestrator"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// Initialize configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logging
	logger := logging.NewLogger(cfg.Logging)

	// Initialize orchestrator
	orch, err := orchestrator.New(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize orchestrator")
	}

	// Initialize API server
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(logging.GinLogger(logger))

	// Setup API routes
	apiHandler := api.NewHandler(orch, logger)
	api.SetupRoutes(router, apiHandler)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.WithFields(logrus.Fields{
			"port": cfg.Server.Port,
			"mode": cfg.Server.Mode,
		}).Info("Starting VM Orchestrator API server")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Fatal("Server forced to shutdown")
	}

	// Close orchestrator connections
	if err := orch.Close(); err != nil {
		logger.WithError(err).Error("Error closing orchestrator connections")
	}

	logger.Info("Server exited")
}
