package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"vm-orchestrator/internal/api"
	"vm-orchestrator/internal/config"
	"vm-orchestrator/internal/logging"
	"vm-orchestrator/internal/orchestrator"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

const (
	appName    = "VM Orchestrator"
	appVersion = "1.0.0"
)

func main() {
	// Print banner
	printBanner()

	// Initialize configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logging
	logger := logging.NewLogger(cfg.Logging)
	logger.WithFields(logrus.Fields{
		"version": appVersion,
		"config":  "loaded",
	}).Info("Starting VM Orchestrator")

	// Initialize orchestrator
	orch, err := orchestrator.New(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize orchestrator")
	}

	// Initialize API server
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(logging.GinLogger(logger))

	// Setup API routes
	apiHandler := api.NewHandler(orch, logger)
	api.SetupRoutes(router, apiHandler)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.WithFields(logrus.Fields{
			"port": cfg.Server.Port,
			"mode": cfg.Server.Mode,
		}).Info("Starting VM Orchestrator API server")

		if cfg.Security.EnableHTTPS {
			if err := server.ListenAndServeTLS(cfg.Security.CertFile, cfg.Security.KeyFile); err != nil && err != http.ErrServerClosed {
				logger.WithError(err).Fatal("Failed to start HTTPS server")
			}
		} else {
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				logger.WithError(err).Fatal("Failed to start HTTP server")
			}
		}
	}()

	// Print startup information
	printStartupInfo(cfg, logger)

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.WithError(err).Fatal("Server forced to shutdown")
	}

	// Close orchestrator connections
	if err := orch.Close(); err != nil {
		logger.WithError(err).Error("Error closing orchestrator connections")
	}

	logger.Info("Server exited")
}

func printBanner() {
	banner := `
██╗   ██╗███╗   ███╗     ██████╗ ██████╗  ██████╗██╗  ██╗███████╗███████╗████████╗██████╗  █████╗ ████████╗ ██████╗ ██████╗
██║   ██║████╗ ████║    ██╔═══██╗██╔══██╗██╔════╝██║  ██║██╔════╝██╔════╝╚══██╔══╝██╔══██╗██╔══██╗╚══██╔══╝██╔═══██╗██╔══██╗
██║   ██║██╔████╔██║    ██║   ██║██████╔╝██║     ███████║█████╗  ███████╗   ██║   ██████╔╝███████║   ██║   ██║   ██║██████╔╝
╚██╗ ██╔╝██║╚██╔╝██║    ██║   ██║██╔══██╗██║     ██╔══██║██╔══╝  ╚════██║   ██║   ██╔══██╗██╔══██║   ██║   ██║   ██║██╔══██╗
 ╚████╔╝ ██║ ╚═╝ ██║    ╚██████╔╝██║  ██║╚██████╗██║  ██║███████╗███████║   ██║   ██║  ██║██║  ██║   ██║   ╚██████╔╝██║  ██║
  ╚═══╝  ╚═╝     ╚═╝     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
`
	fmt.Println(banner)
	fmt.Printf("%s v%s\n", appName, appVersion)
	fmt.Println("Multi-hypervisor Virtual Machine Management Platform")
	fmt.Println("================================================================================")
}

func printStartupInfo(cfg *config.Config, logger *logrus.Logger) {
	protocol := "HTTP"
	if cfg.Security.EnableHTTPS {
		protocol = "HTTPS"
	}

	fmt.Printf("\n🚀 Server started successfully!\n")
	fmt.Printf("📡 %s Server: http://localhost:%d\n", protocol, cfg.Server.Port)
	fmt.Printf("🏥 Health Check: http://localhost:%d/health\n", cfg.Server.Port)
	fmt.Printf("📚 API Documentation: http://localhost:%d/api/docs\n", cfg.Server.Port)
	fmt.Printf("🔧 API Base URL: http://localhost:%d/api/v1\n", cfg.Server.Port)

	// Print hypervisor information
	fmt.Printf("\n📊 Configured Hypervisors:\n")
	if len(cfg.Hypervisors.ESXi) > 0 {
		fmt.Printf("   ESXi Hosts: %d\n", len(cfg.Hypervisors.ESXi))
		for _, esxi := range cfg.Hypervisors.ESXi {
			fmt.Printf("   - %s (%s)\n", esxi.Name, esxi.Host)
		}
	}
	if len(cfg.Hypervisors.Proxmox) > 0 {
		fmt.Printf("   Proxmox Hosts: %d\n", len(cfg.Hypervisors.Proxmox))
		for _, proxmox := range cfg.Hypervisors.Proxmox {
			fmt.Printf("   - %s (%s)\n", proxmox.Name, proxmox.Host)
		}
	}

	fmt.Printf("\n⚙️  Configuration:\n")
	fmt.Printf("   Mode: %s\n", cfg.Server.Mode)
	fmt.Printf("   Log Level: %s\n", cfg.Logging.Level)
	fmt.Printf("   Log Format: %s\n", cfg.Logging.Format)
	if cfg.Security.EnableAuth {
		fmt.Printf("   Authentication: Enabled\n")
	} else {
		fmt.Printf("   Authentication: Disabled\n")
	}

	fmt.Printf("\n📖 Quick Start Examples:\n")
	fmt.Printf("   # Check health\n")
	fmt.Printf("   curl http://localhost:%d/health\n", cfg.Server.Port)
	fmt.Printf("\n   # List hypervisors\n")
	fmt.Printf("   curl http://localhost:%d/api/v1/hypervisors\n", cfg.Server.Port)
	fmt.Printf("\n   # List all VMs\n")
	fmt.Printf("   curl http://localhost:%d/api/v1/vms\n", cfg.Server.Port)

	fmt.Printf("\n🛑 Press Ctrl+C to stop the server\n")
	fmt.Println("================================================================================")

	logger.WithFields(logrus.Fields{
		"port":          cfg.Server.Port,
		"mode":          cfg.Server.Mode,
		"https":         cfg.Security.EnableHTTPS,
		"auth":          cfg.Security.EnableAuth,
		"esxi_hosts":    len(cfg.Hypervisors.ESXi),
		"proxmox_hosts": len(cfg.Hypervisors.Proxmox),
	}).Info("VM Orchestrator started successfully")
}
