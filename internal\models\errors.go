package models

import (
	"fmt"
	"net/http"
)

// ErrorCode represents specific error types
type ErrorCode string

const (
	// General errors
	ErrorCodeInternal        ErrorCode = "INTERNAL_ERROR"
	ErrorCodeInvalidRequest  ErrorCode = "INVALID_REQUEST"
	ErrorCodeNotFound        ErrorCode = "NOT_FOUND"
	ErrorCodeUnauthorized    ErrorCode = "UNAUTHORIZED"
	ErrorCodeForbidden       ErrorCode = "FORBIDDEN"
	ErrorCodeConflict        ErrorCode = "CONFLICT"
	ErrorCodeTimeout         ErrorCode = "TIMEOUT"

	// Hypervisor connection errors
	ErrorCodeConnectionFailed ErrorCode = "CONNECTION_FAILED"
	ErrorCodeAuthFailed       ErrorCode = "AUTH_FAILED"
	ErrorCodeDisconnected     ErrorCode = "DISCONNECTED"

	// VM operation errors
	ErrorCodeVMNotFound       ErrorCode = "VM_NOT_FOUND"
	ErrorCodeVMAlreadyExists  ErrorCode = "VM_ALREADY_EXISTS"
	ErrorCodeVMInvalidState   ErrorCode = "VM_INVALID_STATE"
	ErrorCodeVMOperationFailed ErrorCode = "VM_OPERATION_FAILED"

	// Template operation errors
	ErrorCodeTemplateNotFound      ErrorCode = "TEMPLATE_NOT_FOUND"
	ErrorCodeTemplateAlreadyExists ErrorCode = "TEMPLATE_ALREADY_EXISTS"
	ErrorCodeTemplateInvalid       ErrorCode = "TEMPLATE_INVALID"

	// Snapshot operation errors
	ErrorCodeSnapshotNotFound      ErrorCode = "SNAPSHOT_NOT_FOUND"
	ErrorCodeSnapshotAlreadyExists ErrorCode = "SNAPSHOT_ALREADY_EXISTS"
	ErrorCodeSnapshotOperationFailed ErrorCode = "SNAPSHOT_OPERATION_FAILED"

	// Resource errors
	ErrorCodeInsufficientResources ErrorCode = "INSUFFICIENT_RESOURCES"
	ErrorCodeDatastoreNotFound     ErrorCode = "DATASTORE_NOT_FOUND"
	ErrorCodeNetworkNotFound       ErrorCode = "NETWORK_NOT_FOUND"
)

// APIError represents a structured error response
type APIError struct {
	Code      ErrorCode `json:"code"`
	Message   string    `json:"message"`
	Details   string    `json:"details,omitempty"`
	RequestID string    `json:"request_id,omitempty"`
}

// Error implements the error interface
func (e *APIError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// HTTPStatusCode returns the appropriate HTTP status code for the error
func (e *APIError) HTTPStatusCode() int {
	switch e.Code {
	case ErrorCodeInvalidRequest, ErrorCodeTemplateInvalid:
		return http.StatusBadRequest
	case ErrorCodeUnauthorized, ErrorCodeAuthFailed:
		return http.StatusUnauthorized
	case ErrorCodeForbidden:
		return http.StatusForbidden
	case ErrorCodeNotFound, ErrorCodeVMNotFound, ErrorCodeTemplateNotFound, 
		 ErrorCodeSnapshotNotFound, ErrorCodeDatastoreNotFound, ErrorCodeNetworkNotFound:
		return http.StatusNotFound
	case ErrorCodeConflict, ErrorCodeVMAlreadyExists, ErrorCodeTemplateAlreadyExists, 
		 ErrorCodeSnapshotAlreadyExists, ErrorCodeVMInvalidState:
		return http.StatusConflict
	case ErrorCodeTimeout:
		return http.StatusRequestTimeout
	case ErrorCodeInsufficientResources:
		return http.StatusUnprocessableEntity
	case ErrorCodeConnectionFailed, ErrorCodeDisconnected, ErrorCodeVMOperationFailed, 
		 ErrorCodeSnapshotOperationFailed:
		return http.StatusServiceUnavailable
	default:
		return http.StatusInternalServerError
	}
}

// NewAPIError creates a new API error
func NewAPIError(code ErrorCode, message string, details ...string) *APIError {
	err := &APIError{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewInternalError creates a new internal server error
func NewInternalError(message string, details ...string) *APIError {
	return NewAPIError(ErrorCodeInternal, message, details...)
}

// NewNotFoundError creates a new not found error
func NewNotFoundError(resource string, id string) *APIError {
	return NewAPIError(ErrorCodeNotFound, fmt.Sprintf("%s not found", resource), fmt.Sprintf("ID: %s", id))
}

// NewValidationError creates a new validation error
func NewValidationError(message string, details ...string) *APIError {
	return NewAPIError(ErrorCodeInvalidRequest, message, details...)
}

// NewConnectionError creates a new connection error
func NewConnectionError(hypervisor string, details ...string) *APIError {
	return NewAPIError(ErrorCodeConnectionFailed, fmt.Sprintf("Failed to connect to %s", hypervisor), details...)
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success   bool        `json:"success"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	RequestID string      `json:"request_id,omitempty"`
	Timestamp string      `json:"timestamp"`
}

// NewSuccessResponse creates a new success response
func NewSuccessResponse(data interface{}) *APIResponse {
	return &APIResponse{
		Success: true,
		Data:    data,
	}
}

// NewErrorResponse creates a new error response
func NewErrorResponse(err *APIError) *APIResponse {
	return &APIResponse{
		Success: false,
		Error:   err,
	}
}
