package models

import "time"

// SnapshotState represents the current state of a snapshot
type SnapshotState string

const (
	SnapshotStateReady   SnapshotState = "ready"
	SnapshotStateCreating SnapshotState = "creating"
	SnapshotStateDeleting SnapshotState = "deleting"
	SnapshotStateError   SnapshotState = "error"
)

// Snapshot represents a VM snapshot
type Snapshot struct {
	ID           string        `json:"id"`
	Name         string        `json:"name"`
	Description  string        `json:"description"`
	VMID         string        `json:"vm_id"`
	VMName       string        `json:"vm_name"`
	HypervisorID string        `json:"hypervisor_id"`
	Hypervisor   string        `json:"hypervisor"` // "esxi" or "proxmox"
	State        SnapshotState `json:"state"`
	SizeGB       int           `json:"size_gb"`
	ParentID     string        `json:"parent_id,omitempty"`
	Children     []string      `json:"children,omitempty"`
	CreatedAt    time.Time     `json:"created_at"`
	UpdatedAt    time.Time     `json:"updated_at"`
}

// SnapshotCreateRequest represents a request to create a snapshot
type SnapshotCreateRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Memory      bool   `json:"memory"` // Include memory state
	Quiesce     bool   `json:"quiesce"` // Quiesce file system
}

// SnapshotRestoreRequest represents a request to restore from a snapshot
type SnapshotRestoreRequest struct {
	PowerOff bool `json:"power_off"` // Power off VM before restore
}

// SnapshotListResponse represents a paginated list of snapshots
type SnapshotListResponse struct {
	Snapshots  []Snapshot `json:"snapshots"`
	Total      int        `json:"total"`
	Page       int        `json:"page"`
	PageSize   int        `json:"page_size"`
	TotalPages int        `json:"total_pages"`
}

// SnapshotTree represents a hierarchical view of snapshots
type SnapshotTree struct {
	Snapshot *Snapshot       `json:"snapshot"`
	Children []*SnapshotTree `json:"children,omitempty"`
}
