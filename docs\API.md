# VM Orchestrator API Documentation

## Overview

The VM Orchestrator provides a comprehensive REST API for managing virtual machines across multiple hypervisor platforms including VMware ESXi and Proxmox VE.

## Base URL

```
http://localhost:8080/api/v1
```

## Authentication

Currently, the API supports optional authentication. When enabled, include the API key in the request headers:

```
Authorization: Bearer <api-key>
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "request_id": "20240101120000-abc12345",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details"
  },
  "request_id": "20240101120000-abc12345",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Endpoints

### Health Check

#### GET /health
Returns the health status of the service.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "hypervisors": 2,
    "version": "1.0.0"
  }
}
```

### Hypervisors

#### GET /api/v1/hypervisors
List all connected hypervisors.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "esxi-host-1",
      "name": "esxi-host-1",
      "type": "esxi",
      "version": "7.0.3",
      "address": "*************"
    }
  ]
}
```

### Virtual Machines

#### GET /api/v1/vms
List all virtual machines across all hypervisors.

**Query Parameters:**
- `hypervisor_id` (string): Filter by hypervisor ID
- `name` (string): Filter by VM name (partial match)
- `state` (string): Filter by VM state (poweredOn, poweredOff, suspended)
- `limit` (int): Limit number of results
- `offset` (int): Offset for pagination

**Response:**
```json
{
  "success": true,
  "data": {
    "vms": [
      {
        "id": "vm-123",
        "name": "test-vm",
        "state": "poweredOn",
        "hypervisor_id": "esxi-host-1",
        "hypervisor": "esxi",
        "cpu": {
          "cores": 2,
          "sockets": 1,
          "threads": 1
        },
        "memory": {
          "size_mb": 2048
        },
        "disks": [...],
        "networks": [...],
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10,
    "total_pages": 1
  }
}
```

#### GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}
Get a specific virtual machine.

#### POST /api/v1/vms
Create a new virtual machine.

**Request Body:**
```json
{
  "name": "test-vm",
  "hypervisor_id": "esxi-host-1",
  "cpu": {
    "cores": 2,
    "sockets": 1,
    "threads": 1
  },
  "memory": {
    "size_mb": 2048
  },
  "disks": [
    {
      "name": "disk0",
      "size_gb": 20,
      "datastore": "datastore1",
      "type": "thin"
    }
  ],
  "networks": [
    {
      "name": "net0",
      "network": "VM Network",
      "type": "vmxnet3"
    }
  ]
}
```

#### PUT /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}
Update virtual machine configuration.

#### DELETE /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}
Delete a virtual machine.

**Query Parameters:**
- `force` (boolean): Force delete even if VM is running

#### POST /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/actions
Perform an action on a virtual machine.

**Request Body:**
```json
{
  "action": "start",
  "force": false
}
```

**Supported Actions:**
- `start`: Power on the VM
- `stop`: Power off the VM
- `restart`: Restart the VM
- `suspend`: Suspend the VM
- `resume`: Resume the VM

#### GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/metrics
Get performance metrics for a VM.

#### GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/console
Get console URL for a VM.

### Hosts

#### GET /api/v1/hypervisors/{hypervisor_id}/hosts
List hypervisor hosts.

#### GET /api/v1/hypervisors/{hypervisor_id}/hosts/{host_id}
Get specific host information.

#### GET /api/v1/hypervisors/{hypervisor_id}/hosts/{host_id}/metrics
Get host performance metrics.

### Templates

#### GET /api/v1/hypervisors/{hypervisor_id}/templates
List VM templates.

#### POST /api/v1/hypervisors/{hypervisor_id}/templates
Create a new template from a VM.

**Request Body:**
```json
{
  "name": "ubuntu-template",
  "description": "Ubuntu 20.04 template",
  "source_vm_id": "vm-123",
  "os": "ubuntu",
  "version": "20.04"
}
```

#### GET /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}
Get specific template.

#### PUT /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}
Update template.

#### DELETE /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}
Delete template.

#### POST /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}/deploy
Deploy a VM from template.

**Request Body:**
```json
{
  "vm_name": "new-vm",
  "hypervisor_id": "esxi-host-1",
  "cpu": {
    "cores": 4
  },
  "memory": {
    "size_mb": 4096
  },
  "power_on": true
}
```

### Snapshots

#### GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots
List VM snapshots.

#### POST /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots
Create a snapshot.

**Request Body:**
```json
{
  "name": "snapshot-1",
  "description": "Before update",
  "memory": true,
  "quiesce": false
}
```

#### GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots/tree
Get snapshot tree.

#### POST /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots/{snapshot_id}/restore
Restore from snapshot.

#### DELETE /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots/{snapshot_id}
Delete snapshot.

### Storage

#### GET /api/v1/hypervisors/{hypervisor_id}/storage/datastores
List datastores.

#### GET /api/v1/hypervisors/{hypervisor_id}/storage/datastores/{datastore_id}
Get specific datastore.

#### GET /api/v1/hypervisors/{hypervisor_id}/storage/networks
List networks.

#### GET /api/v1/hypervisors/{hypervisor_id}/storage/networks/{network_id}
Get specific network.

## Error Codes

| Code | Description |
|------|-------------|
| INTERNAL_ERROR | Internal server error |
| INVALID_REQUEST | Invalid request format or parameters |
| NOT_FOUND | Resource not found |
| UNAUTHORIZED | Authentication required |
| FORBIDDEN | Access denied |
| CONFLICT | Resource conflict (e.g., VM already exists) |
| TIMEOUT | Request timeout |
| CONNECTION_FAILED | Failed to connect to hypervisor |
| VM_NOT_FOUND | Virtual machine not found |
| VM_INVALID_STATE | VM is in invalid state for operation |
| INSUFFICIENT_RESOURCES | Not enough resources available |

## Rate Limiting

The API implements rate limiting to prevent abuse. Current limits:
- 100 requests per minute per IP address
- 1000 requests per hour per API key

## Examples

See the `examples/` directory for complete usage examples in various programming languages.
