package models

import "time"

// HostState represents the current state of a hypervisor host
type HostState string

const (
	HostStateConnected    HostState = "connected"
	HostStateDisconnected HostState = "disconnected"
	HostStateMaintenance  HostState = "maintenance"
	HostStateUnknown      HostState = "unknown"
)

// Host represents a hypervisor host
type Host struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Type         string            `json:"type"` // "esxi" or "proxmox"
	Address      string            `json:"address"`
	State        HostState         `json:"state"`
	Version      string            `json:"version"`
	CPU          HostCPUInfo       `json:"cpu"`
	Memory       HostMemoryInfo    `json:"memory"`
	Storage      []HostStorageInfo `json:"storage"`
	Networks     []HostNetworkInfo `json:"networks"`
	VMCount      int               `json:"vm_count"`
	Tags         map[string]string `json:"tags,omitempty"`
	LastSeen     time.Time         `json:"last_seen"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// HostCPUInfo represents CPU information for a host
type HostCPUInfo struct {
	Model       string  `json:"model"`
	Cores       int     `json:"cores"`
	Threads     int     `json:"threads"`
	Speed       int     `json:"speed"` // MHz
	UsagePercent float64 `json:"usage_percent"`
}

// HostMemoryInfo represents memory information for a host
type HostMemoryInfo struct {
	TotalMB      int     `json:"total_mb"`
	UsedMB       int     `json:"used_mb"`
	FreeMB       int     `json:"free_mb"`
	UsagePercent float64 `json:"usage_percent"`
}

// HostStorageInfo represents storage information for a host
type HostStorageInfo struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	Type         string  `json:"type"` // "local", "nfs", "iscsi", etc.
	TotalGB      int     `json:"total_gb"`
	UsedGB       int     `json:"used_gb"`
	FreeGB       int     `json:"free_gb"`
	UsagePercent float64 `json:"usage_percent"`
}

// HostNetworkInfo represents network information for a host
type HostNetworkInfo struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"` // "vswitch", "bridge", etc.
	VLAN    int    `json:"vlan,omitempty"`
	Active  bool   `json:"active"`
}

// HostMetrics represents performance metrics for a host
type HostMetrics struct {
	HostID    string         `json:"host_id"`
	Timestamp time.Time      `json:"timestamp"`
	CPU       CPUMetrics     `json:"cpu"`
	Memory    MemoryMetrics  `json:"memory"`
	Storage   []StorageMetrics `json:"storage"`
	Network   []NetworkMetrics `json:"network"`
}

// StorageMetrics represents storage performance metrics
type StorageMetrics struct {
	StorageID    string  `json:"storage_id"`
	ReadIOPS     float64 `json:"read_iops"`
	WriteIOPS    float64 `json:"write_iops"`
	ReadMBps     float64 `json:"read_mbps"`
	WriteMBps    float64 `json:"write_mbps"`
	UsagePercent float64 `json:"usage_percent"`
}

// HostListResponse represents a paginated list of hosts
type HostListResponse struct {
	Hosts      []Host `json:"hosts"`
	Total      int    `json:"total"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
	TotalPages int    `json:"total_pages"`
}
