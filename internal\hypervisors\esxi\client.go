package esxi

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"

	"github.com/vmware/govmomi"
	"github.com/vmware/govmomi/find"
	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/session/cache"
	"github.com/vmware/govmomi/vim25/soap"
	"github.com/vmware/govmomi/vim25/types"
)

// Client implements the HypervisorClient interface for VMware ESXi
type Client struct {
	config     interfaces.HypervisorConfig
	client     *govmomi.Client
	finder     *find.Finder
	datacenter *object.Datacenter
	connected  bool
}

// NewClient creates a new ESXi client
func NewClient(config interfaces.HypervisorConfig) *Client {
	return &Client{
		config:    config,
		connected: false,
	}
}

// Connect establishes connection to ESXi host
func (c *Client) Connect(ctx context.Context) error {
	// Parse URL
	u, err := soap.ParseURL(c.config.Host)
	if err != nil {
		return models.NewConnectionError("ESXi", fmt.Sprintf("Invalid URL: %v", err))
	}

	// Set credentials
	u.User = url.UserPassword(c.config.Username, c.config.Password)

	// Create session cache
	s := &cache.Session{
		URL:      u,
		Insecure: c.config.Insecure,
	}

	// Connect to vCenter/ESXi
	c.client = new(govmomi.Client)
	err = s.Login(ctx, c.client, nil)
	if err != nil {
		return models.NewConnectionError("ESXi", fmt.Sprintf("Login failed: %v", err))
	}

	// Set timeout if specified
	if c.config.Timeout > 0 {
		c.client.Client.Transport.(*soap.Client).RoundTripper = &soap.DefaultRoundTripper{
			RoundTripper: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: c.config.Insecure},
			},
			Timeout: time.Duration(c.config.Timeout) * time.Second,
		}
	}

	// Create finder
	c.finder = find.NewFinder(c.client.Client, true)

	// Find datacenter
	dc, err := c.finder.DefaultDatacenter(ctx)
	if err != nil {
		return models.NewConnectionError("ESXi", fmt.Sprintf("Failed to find datacenter: %v", err))
	}
	c.datacenter = dc
	c.finder.SetDatacenter(dc)

	c.connected = true
	return nil
}

// Disconnect closes the connection to ESXi host
func (c *Client) Disconnect(ctx context.Context) error {
	if c.client != nil {
		err := c.client.Logout(ctx)
		c.connected = false
		return err
	}
	return nil
}

// IsConnected returns the connection status
func (c *Client) IsConnected() bool {
	return c.connected && c.client != nil
}

// GetInfo returns hypervisor information
func (c *Client) GetInfo() *interfaces.HypervisorInfo {
	info := &interfaces.HypervisorInfo{
		ID:      c.config.ID,
		Name:    c.config.Name,
		Type:    "esxi",
		Address: c.config.Host,
	}

	if c.client != nil && c.connected {
		info.Version = c.client.ServiceContent.About.Version
	}

	return info
}

// ListVMs returns a list of virtual machines
func (c *Client) ListVMs(ctx context.Context, filters interfaces.VMFilters) ([]models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vms, err := c.finder.VirtualMachineList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list VMs", err.Error())
	}

	var result []models.VirtualMachine
	for _, vm := range vms {
		vmModel, err := c.convertVMToModel(ctx, vm)
		if err != nil {
			continue // Skip VMs that can't be converted
		}

		// Apply filters
		if c.matchesFilters(vmModel, filters) {
			result = append(result, *vmModel)
		}
	}

	// Apply pagination
	if filters.Limit > 0 {
		start := filters.Offset
		end := start + filters.Limit
		if start >= len(result) {
			return []models.VirtualMachine{}, nil
		}
		if end > len(result) {
			end = len(result)
		}
		result = result[start:end]
	}

	return result, nil
}

// GetVM returns a specific virtual machine
func (c *Client) GetVM(ctx context.Context, vmID string) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, vmID)
	if err != nil {
		return nil, models.NewNotFoundError("VM", vmID)
	}

	return c.convertVMToModel(ctx, vm)
}

// CreateVM creates a new virtual machine
func (c *Client) CreateVM(ctx context.Context, req models.VMCreateRequest) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	// Find resource pool
	pool, err := c.finder.DefaultResourcePool(ctx)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to find resource pool", err.Error())
	}

	// Find datastore
	var datastore *object.Datastore
	if len(req.Disks) > 0 && req.Disks[0].Datastore != "" {
		datastore, err = c.finder.Datastore(ctx, req.Disks[0].Datastore)
		if err != nil {
			return nil, models.NewNotFoundError("Datastore", req.Disks[0].Datastore)
		}
	} else {
		datastore, err = c.finder.DefaultDatastore(ctx)
		if err != nil {
			return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to find datastore", err.Error())
		}
	}

	// Create VM configuration
	config := c.buildVMConfig(req)

	// Create VM
	task, err := pool.CreateVM(ctx, config, datastore.Folder())
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to create VM", err.Error())
	}

	// Wait for task completion
	info, err := task.WaitForResult(ctx, nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "VM creation task failed", err.Error())
	}

	// Get created VM
	vm := object.NewVirtualMachine(c.client.Client, info.Result.(types.ManagedObjectReference))
	return c.convertVMToModel(ctx, vm)
}

// Helper method to build VM configuration
func (c *Client) buildVMConfig(req models.VMCreateRequest) types.VirtualMachineConfigSpec {
	config := types.VirtualMachineConfigSpec{
		Name:     req.Name,
		NumCPUs:  int32(req.CPU.Cores),
		MemoryMB: int64(req.Memory.SizeMB),
		Files: &types.VirtualMachineFileInfo{
			VmPathName: fmt.Sprintf("[%s]", req.Disks[0].Datastore),
		},
	}

	// Add network devices
	for i, network := range req.Networks {
		device := c.createNetworkDevice(network, int32(i))
		config.DeviceChange = append(config.DeviceChange, &types.VirtualDeviceConfigSpec{
			Operation: types.VirtualDeviceConfigSpecOperationAdd,
			Device:    device,
		})
	}

	// Add disk devices
	for i, disk := range req.Disks {
		device := c.createDiskDevice(disk, int32(i))
		config.DeviceChange = append(config.DeviceChange, &types.VirtualDeviceConfigSpec{
			Operation: types.VirtualDeviceConfigSpecOperationAdd,
			Device:    device,
		})
	}

	return config
}

// Helper method to create network device
func (c *Client) createNetworkDevice(network models.NetworkConfig, key int32) types.BaseVirtualDevice {
	backing := &types.VirtualEthernetCardNetworkBackingInfo{
		VirtualDeviceDeviceBackingInfo: types.VirtualDeviceDeviceBackingInfo{
			DeviceName: network.Network,
		},
	}

	device := &types.VirtualVmxnet3{
		VirtualVmxnet: types.VirtualVmxnet{
			VirtualEthernetCard: types.VirtualEthernetCard{
				VirtualDevice: types.VirtualDevice{
					Key:     key + 4000,
					Backing: backing,
				},
			},
		},
	}

	return device
}

// Helper method to create disk device
func (c *Client) createDiskDevice(disk models.DiskConfig, key int32) types.BaseVirtualDevice {
	backing := &types.VirtualDiskFlatVer2BackingInfo{
		DiskMode:        string(types.VirtualDiskModePersistent),
		ThinProvisioned: types.NewBool(disk.Type == "thin"),
		VirtualDeviceFileBackingInfo: types.VirtualDeviceFileBackingInfo{
			FileName: fmt.Sprintf("[%s] %s/%s.vmdk", disk.Datastore, disk.Name, disk.Name),
		},
	}

	device := &types.VirtualDisk{
		VirtualDevice: types.VirtualDevice{
			Key:     key + 2000,
			Backing: backing,
		},
		CapacityInKB: int64(disk.SizeGB * 1024 * 1024),
	}

	return device
}
