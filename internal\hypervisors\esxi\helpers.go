package esxi

import (
	"context"
	"strings"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/types"
)

// convertVMToModel converts a govmomi VM object to our VM model
func (c *Client) convertVMToModel(ctx context.Context, vm *object.VirtualMachine) (*models.VirtualMachine, error) {
	var vmObj object.VirtualMachine
	err := vm.Properties(ctx, vm.Reference(), []string{"summary", "config", "runtime"}, &vmObj)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get VM properties", err.Error())
	}

	summary := vmObj.Summary
	config := vmObj.Config
	runtime := vmObj.Runtime

	// Convert power state
	state := c.convertPowerState(runtime.PowerState)

	// Extract CPU configuration
	cpuConfig := models.CPUConfig{
		Cores:   int(config.NumCpu),
		Sockets: int(config.NumCpu), // Simplified - actual socket count would need more complex logic
		Threads: 1,
	}

	// Extract memory configuration
	memoryConfig := models.MemoryConfig{
		SizeMB: int(config.MemorySizeMB),
	}

	// Extract disk configurations
	var disks []models.DiskConfig
	for _, device := range config.Hardware.Device {
		if disk, ok := device.(*types.VirtualDisk); ok {
			diskConfig := models.DiskConfig{
				ID:     disk.DeviceInfo.GetDescription().Label,
				Name:   disk.DeviceInfo.GetDescription().Label,
				SizeGB: int(disk.CapacityInKB / 1024 / 1024),
				Type:   "thick", // Default, would need to check backing info for actual type
			}
			
			if backing, ok := disk.Backing.(*types.VirtualDiskFlatVer2BackingInfo); ok {
				if backing.ThinProvisioned != nil && *backing.ThinProvisioned {
					diskConfig.Type = "thin"
				}
				// Extract datastore name from file path
				if backing.FileName != "" {
					parts := strings.Split(backing.FileName, "]")
					if len(parts) > 0 {
						diskConfig.Datastore = strings.Trim(parts[0], "[ ")
					}
				}
			}
			
			disks = append(disks, diskConfig)
		}
	}

	// Extract network configurations
	var networks []models.NetworkConfig
	for _, device := range config.Hardware.Device {
		if nic, ok := device.(types.BaseVirtualEthernetCard); ok {
			card := nic.GetVirtualEthernetCard()
			networkConfig := models.NetworkConfig{
				ID:   card.DeviceInfo.GetDescription().Label,
				Name: card.DeviceInfo.GetDescription().Label,
				MAC:  card.MacAddress,
				Type: "vmxnet3", // Default, would need to check actual type
			}
			
			if backing, ok := card.Backing.(*types.VirtualEthernetCardNetworkBackingInfo); ok {
				networkConfig.Network = backing.DeviceName
			}
			
			networks = append(networks, networkConfig)
		}
	}

	vm_model := &models.VirtualMachine{
		ID:           vm.Reference().Value,
		Name:         config.Name,
		State:        state,
		HypervisorID: c.config.ID,
		Hypervisor:   "esxi",
		CPU:          cpuConfig,
		Memory:       memoryConfig,
		Disks:        disks,
		Networks:     networks,
		Tags:         make(map[string]string),
		CreatedAt:    time.Now(), // Would need to get actual creation time from VM
		UpdatedAt:    time.Now(),
	}

	return vm_model, nil
}

// convertPowerState converts VMware power state to our model
func (c *Client) convertPowerState(state types.VirtualMachinePowerState) models.VMState {
	switch state {
	case types.VirtualMachinePowerStatePoweredOn:
		return models.VMStatePoweredOn
	case types.VirtualMachinePowerStatePoweredOff:
		return models.VMStatePoweredOff
	case types.VirtualMachinePowerStateSuspended:
		return models.VMStateSuspended
	default:
		return models.VMStateUnknown
	}
}

// matchesFilters checks if a VM matches the given filters
func (c *Client) matchesFilters(vm *models.VirtualMachine, filters interfaces.VMFilters) bool {
	// Name filter
	if filters.Name != "" && !strings.Contains(strings.ToLower(vm.Name), strings.ToLower(filters.Name)) {
		return false
	}

	// State filter
	if filters.State != "" && vm.State != filters.State {
		return false
	}

	// Hypervisor ID filter
	if filters.HypervisorID != "" && vm.HypervisorID != filters.HypervisorID {
		return false
	}

	// Tags filter
	if len(filters.Tags) > 0 {
		for key, value := range filters.Tags {
			if vmValue, exists := vm.Tags[key]; !exists || vmValue != value {
				return false
			}
		}
	}

	return true
}

// ListHosts returns a list of ESXi hosts
func (c *Client) ListHosts(ctx context.Context) ([]models.Host, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	hosts, err := c.finder.HostSystemList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list hosts", err.Error())
	}

	var result []models.Host
	for _, host := range hosts {
		hostModel, err := c.convertHostToModel(ctx, host)
		if err != nil {
			continue // Skip hosts that can't be converted
		}
		result = append(result, *hostModel)
	}

	return result, nil
}

// GetHost returns a specific host
func (c *Client) GetHost(ctx context.Context, hostID string) (*models.Host, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	host, err := c.finder.HostSystem(ctx, hostID)
	if err != nil {
		return nil, models.NewNotFoundError("Host", hostID)
	}

	return c.convertHostToModel(ctx, host)
}

// convertHostToModel converts a govmomi host object to our host model
func (c *Client) convertHostToModel(ctx context.Context, host *object.HostSystem) (*models.Host, error) {
	var hostObj object.HostSystem
	err := host.Properties(ctx, host.Reference(), []string{"summary", "config", "hardware"}, &hostObj)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get host properties", err.Error())
	}

	summary := hostObj.Summary
	config := hostObj.Config
	hardware := hostObj.Hardware

	// Convert connection state
	state := c.convertHostState(summary.Runtime.ConnectionState)

	// Extract CPU information
	cpuInfo := models.HostCPUInfo{
		Model:   hardware.CpuInfo.ModelName,
		Cores:   int(hardware.CpuInfo.NumCpuCores),
		Threads: int(hardware.CpuInfo.NumCpuThreads),
		Speed:   int(hardware.CpuInfo.Hz / 1000000), // Convert Hz to MHz
	}

	// Extract memory information
	memoryInfo := models.HostMemoryInfo{
		TotalMB: int(hardware.MemorySize / 1024 / 1024),
		UsedMB:  int(summary.QuickStats.OverallMemoryUsage),
	}
	memoryInfo.FreeMB = memoryInfo.TotalMB - memoryInfo.UsedMB
	if memoryInfo.TotalMB > 0 {
		memoryInfo.UsagePercent = float64(memoryInfo.UsedMB) / float64(memoryInfo.TotalMB) * 100
	}

	hostModel := &models.Host{
		ID:        host.Reference().Value,
		Name:      summary.Config.Name,
		Type:      "esxi",
		Address:   config.Network.Vnic[0].Spec.Ip.IpAddress, // Primary management IP
		State:     state,
		Version:   config.Product.Version,
		CPU:       cpuInfo,
		Memory:    memoryInfo,
		Storage:   []models.HostStorageInfo{}, // Would need additional API calls to populate
		Networks:  []models.HostNetworkInfo{}, // Would need additional API calls to populate
		VMCount:   0,                          // Would need additional API calls to populate
		Tags:      make(map[string]string),
		LastSeen:  time.Now(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return hostModel, nil
}

// convertHostState converts VMware host connection state to our model
func (c *Client) convertHostState(state types.HostSystemConnectionState) models.HostState {
	switch state {
	case types.HostSystemConnectionStateConnected:
		return models.HostStateConnected
	case types.HostSystemConnectionStateDisconnected:
		return models.HostStateDisconnected
	case types.HostSystemConnectionStateNotResponding:
		return models.HostStateDisconnected
	default:
		return models.HostStateUnknown
	}
}

// GetHostMetrics returns performance metrics for a host
func (c *Client) GetHostMetrics(ctx context.Context, hostID string) (*models.HostMetrics, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	host, err := c.finder.HostSystem(ctx, hostID)
	if err != nil {
		return nil, models.NewNotFoundError("Host", hostID)
	}

	var hostObj object.HostSystem
	err = host.Properties(ctx, host.Reference(), []string{"summary"}, &hostObj)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get host properties", err.Error())
	}

	summary := hostObj.Summary

	metrics := &models.HostMetrics{
		HostID:    hostID,
		Timestamp: time.Now(),
		CPU: models.CPUMetrics{
			UsagePercent: float64(summary.QuickStats.OverallCpuUsage) / float64(summary.Hardware.CpuMhz*summary.Hardware.NumCpuCores) * 100,
			UsageMHz:     float64(summary.QuickStats.OverallCpuUsage),
		},
		Memory: models.MemoryMetrics{
			UsagePercent: float64(summary.QuickStats.OverallMemoryUsage) / float64(summary.Hardware.MemorySize/1024/1024) * 100,
			UsageMB:      float64(summary.QuickStats.OverallMemoryUsage),
			TotalMB:      float64(summary.Hardware.MemorySize / 1024 / 1024),
		},
	}

	return metrics, nil
}
