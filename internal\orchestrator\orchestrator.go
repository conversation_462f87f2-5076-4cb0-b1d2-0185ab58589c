package orchestrator

import (
	"context"
	"fmt"
	"sync"
	"time"

	"vm-orchestrator/internal/config"
	"vm-orchestrator/internal/hypervisors/esxi"
	"vm-orchestrator/internal/hypervisors/proxmox"
	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"

	"github.com/sirupsen/logrus"
)

// Orchestrator manages multiple hypervisor clients and provides a unified interface
type Orchestrator struct {
	config      *config.Config
	logger      *logrus.Logger
	clients     map[string]interfaces.HypervisorClient
	factories   map[string]interfaces.HypervisorFactory
	clientMutex sync.RWMutex
}

// New creates a new orchestrator instance
func New(cfg *config.Config, logger *logrus.Logger) (*Orchestrator, error) {
	o := &Orchestrator{
		config:    cfg,
		logger:    logger,
		clients:   make(map[string]interfaces.HypervisorClient),
		factories: make(map[string]interfaces.HypervisorFactory),
	}

	// Register hypervisor factories
	o.factories["esxi"] = esxi.NewFactory()
	o.factories["proxmox"] = proxmox.NewFactory()

	// Initialize hypervisor clients
	if err := o.initializeClients(); err != nil {
		return nil, err
	}

	return o, nil
}

// initializeClients creates and connects to all configured hypervisors
func (o *Orchestrator) initializeClients() error {
	o.clientMutex.Lock()
	defer o.clientMutex.Unlock()

	// Initialize ESXi clients
	for _, esxiConfig := range o.config.Hypervisors.ESXi {
		config := interfaces.HypervisorConfig{
			ID:       esxiConfig.Name,
			Name:     esxiConfig.Name,
			Type:     "esxi",
			Host:     esxiConfig.Host,
			Username: esxiConfig.Username,
			Password: esxiConfig.Password,
			Insecure: esxiConfig.Insecure,
			Timeout:  30,
		}

		client, err := o.factories["esxi"].CreateClient(config)
		if err != nil {
			o.logger.WithError(err).Errorf("Failed to create ESXi client for %s", esxiConfig.Name)
			continue
		}

		// Connect to hypervisor
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		if err := client.Connect(ctx); err != nil {
			o.logger.WithError(err).Errorf("Failed to connect to ESXi host %s", esxiConfig.Name)
			cancel()
			continue
		}
		cancel()

		o.clients[esxiConfig.Name] = client
		o.logger.Infof("Successfully connected to ESXi host %s", esxiConfig.Name)
	}

	// Initialize Proxmox clients
	for _, proxmoxConfig := range o.config.Hypervisors.Proxmox {
		config := interfaces.HypervisorConfig{
			ID:       proxmoxConfig.Name,
			Name:     proxmoxConfig.Name,
			Type:     "proxmox",
			Host:     proxmoxConfig.Host,
			Username: proxmoxConfig.Username,
			Password: proxmoxConfig.Password,
			Insecure: proxmoxConfig.Insecure,
			Timeout:  30,
		}

		client, err := o.factories["proxmox"].CreateClient(config)
		if err != nil {
			o.logger.WithError(err).Errorf("Failed to create Proxmox client for %s", proxmoxConfig.Name)
			continue
		}

		// Connect to hypervisor
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		if err := client.Connect(ctx); err != nil {
			o.logger.WithError(err).Errorf("Failed to connect to Proxmox host %s", proxmoxConfig.Name)
			cancel()
			continue
		}
		cancel()

		o.clients[proxmoxConfig.Name] = client
		o.logger.Infof("Successfully connected to Proxmox host %s", proxmoxConfig.Name)
	}

	if len(o.clients) == 0 {
		return fmt.Errorf("no hypervisor clients could be initialized")
	}

	return nil
}

// GetClient returns a specific hypervisor client by ID
func (o *Orchestrator) GetClient(hypervisorID string) (interfaces.HypervisorClient, error) {
	o.clientMutex.RLock()
	defer o.clientMutex.RUnlock()

	client, exists := o.clients[hypervisorID]
	if !exists {
		return nil, models.NewNotFoundError("Hypervisor", hypervisorID)
	}

	if !client.IsConnected() {
		return nil, models.NewConnectionError(hypervisorID, "Client is not connected")
	}

	return client, nil
}

// GetAllClients returns all connected hypervisor clients
func (o *Orchestrator) GetAllClients() map[string]interfaces.HypervisorClient {
	o.clientMutex.RLock()
	defer o.clientMutex.RUnlock()

	clients := make(map[string]interfaces.HypervisorClient)
	for id, client := range o.clients {
		if client.IsConnected() {
			clients[id] = client
		}
	}

	return clients
}

// ListVMs returns VMs from all hypervisors or a specific one
func (o *Orchestrator) ListVMs(ctx context.Context, hypervisorID string, filters interfaces.VMFilters) ([]models.VirtualMachine, error) {
	var allVMs []models.VirtualMachine

	if hypervisorID != "" {
		// Get VMs from specific hypervisor
		client, err := o.GetClient(hypervisorID)
		if err != nil {
			return nil, err
		}

		vms, err := client.ListVMs(ctx, filters)
		if err != nil {
			return nil, err
		}

		allVMs = append(allVMs, vms...)
	} else {
		// Get VMs from all hypervisors
		clients := o.GetAllClients()
		for id, client := range clients {
			vms, err := client.ListVMs(ctx, filters)
			if err != nil {
				o.logger.WithError(err).Errorf("Failed to list VMs from hypervisor %s", id)
				continue
			}

			allVMs = append(allVMs, vms...)
		}
	}

	return allVMs, nil
}

// GetVM returns a specific VM by ID and hypervisor
func (o *Orchestrator) GetVM(ctx context.Context, hypervisorID, vmID string) (*models.VirtualMachine, error) {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return nil, err
	}

	return client.GetVM(ctx, vmID)
}

// CreateVM creates a new VM on the specified hypervisor
func (o *Orchestrator) CreateVM(ctx context.Context, req models.VMCreateRequest) (*models.VirtualMachine, error) {
	client, err := o.GetClient(req.HypervisorID)
	if err != nil {
		return nil, err
	}

	return client.CreateVM(ctx, req)
}

// UpdateVM updates an existing VM
func (o *Orchestrator) UpdateVM(ctx context.Context, hypervisorID, vmID string, req models.VMUpdateRequest) (*models.VirtualMachine, error) {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return nil, err
	}

	return client.UpdateVM(ctx, vmID, req)
}

// DeleteVM deletes a VM
func (o *Orchestrator) DeleteVM(ctx context.Context, hypervisorID, vmID string, force bool) error {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return err
	}

	return client.DeleteVM(ctx, vmID, force)
}

// StartVM starts a VM
func (o *Orchestrator) StartVM(ctx context.Context, hypervisorID, vmID string) error {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return err
	}

	return client.StartVM(ctx, vmID)
}

// StopVM stops a VM
func (o *Orchestrator) StopVM(ctx context.Context, hypervisorID, vmID string, force bool) error {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return err
	}

	return client.StopVM(ctx, vmID, force)
}

// RestartVM restarts a VM
func (o *Orchestrator) RestartVM(ctx context.Context, hypervisorID, vmID string, force bool) error {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return err
	}

	return client.RestartVM(ctx, vmID, force)
}

// SuspendVM suspends a VM
func (o *Orchestrator) SuspendVM(ctx context.Context, hypervisorID, vmID string) error {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return err
	}

	return client.SuspendVM(ctx, vmID)
}

// ResumeVM resumes a VM
func (o *Orchestrator) ResumeVM(ctx context.Context, hypervisorID, vmID string) error {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return err
	}

	return client.ResumeVM(ctx, vmID)
}

// GetVMMetrics returns performance metrics for a VM
func (o *Orchestrator) GetVMMetrics(ctx context.Context, hypervisorID, vmID string) (*models.VMMetrics, error) {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return nil, err
	}

	return client.GetVMMetrics(ctx, vmID)
}

// GetVMConsoleURL returns console URL for a VM
func (o *Orchestrator) GetVMConsoleURL(ctx context.Context, hypervisorID, vmID string) (string, error) {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return "", err
	}

	return client.GetVMConsoleURL(ctx, vmID)
}

// ListHosts returns hosts from all hypervisors or a specific one
func (o *Orchestrator) ListHosts(ctx context.Context, hypervisorID string) ([]models.Host, error) {
	var allHosts []models.Host

	if hypervisorID != "" {
		// Get hosts from specific hypervisor
		client, err := o.GetClient(hypervisorID)
		if err != nil {
			return nil, err
		}

		hosts, err := client.ListHosts(ctx)
		if err != nil {
			return nil, err
		}

		allHosts = append(allHosts, hosts...)
	} else {
		// Get hosts from all hypervisors
		clients := o.GetAllClients()
		for id, client := range clients {
			hosts, err := client.ListHosts(ctx)
			if err != nil {
				o.logger.WithError(err).Errorf("Failed to list hosts from hypervisor %s", id)
				continue
			}

			allHosts = append(allHosts, hosts...)
		}
	}

	return allHosts, nil
}

// GetHost returns a specific host
func (o *Orchestrator) GetHost(ctx context.Context, hypervisorID, hostID string) (*models.Host, error) {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return nil, err
	}

	return client.GetHost(ctx, hostID)
}

// GetHostMetrics returns performance metrics for a host
func (o *Orchestrator) GetHostMetrics(ctx context.Context, hypervisorID, hostID string) (*models.HostMetrics, error) {
	client, err := o.GetClient(hypervisorID)
	if err != nil {
		return nil, err
	}

	return client.GetHostMetrics(ctx, hostID)
}

// Close closes all hypervisor connections
func (o *Orchestrator) Close() error {
	o.clientMutex.Lock()
	defer o.clientMutex.Unlock()

	var errors []error
	for id, client := range o.clients {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		if err := client.Disconnect(ctx); err != nil {
			errors = append(errors, fmt.Errorf("failed to disconnect from %s: %v", id, err))
		}
		cancel()
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors during shutdown: %v", errors)
	}

	return nil
}

// GetHypervisorInfo returns information about all connected hypervisors
func (o *Orchestrator) GetHypervisorInfo() []interfaces.HypervisorInfo {
	o.clientMutex.RLock()
	defer o.clientMutex.RUnlock()

	var info []interfaces.HypervisorInfo
	for _, client := range o.clients {
		if client.IsConnected() {
			info = append(info, *client.GetInfo())
		}
	}

	return info
}
