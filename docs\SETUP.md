# VM Orchestrator Setup Guide

## Prerequisites

### System Requirements
- Go 1.21 or later
- Access to VMware ESXi and/or Proxmox VE environments
- Valid credentials for hypervisor connections
- Minimum 512MB RAM
- 100MB disk space

### Supported Hypervisors
- **VMware ESXi**: 6.5, 6.7, 7.0, 8.0
- **Proxmox VE**: 6.x, 7.x, 8.x

## Installation

### Option 1: Download Pre-built Binary

1. Download the latest release from GitHub:
```bash
wget https://github.com/your-org/vm-orchestrator/releases/latest/download/vm-orchestrator-linux-amd64.tar.gz
tar -xzf vm-orchestrator-linux-amd64.tar.gz
cd vm-orchestrator
```

2. Make the binary executable:
```bash
chmod +x orchestrator
```

### Option 2: Build from Source

1. Clone the repository:
```bash
git clone https://github.com/your-org/vm-orchestrator.git
cd vm-orchestrator
```

2. Install dependencies:
```bash
go mod download
```

3. Build the application:
```bash
go build -o orchestrator cmd/orchestrator/main.go
```

## Configuration

### 1. Create Configuration File

Copy the example configuration:
```bash
cp config.example.yaml config.yaml
```

### 2. Configure Hypervisors

Edit `config.yaml` to add your hypervisor connections:

```yaml
hypervisors:
  esxi:
    - name: "esxi-host-1"
      host: "*************"
      username: "root"
      password: "your-password"
      insecure: true  # Set to false for production with valid certificates

  proxmox:
    - name: "proxmox-host-1"
      host: "*************"
      username: "root@pam"
      password: "your-password"
      insecure: true  # Set to false for production with valid certificates
```

### 3. Configure Server Settings

```yaml
server:
  port: 8080
  mode: release  # Use 'debug' for development
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60
```

### 4. Configure Logging

```yaml
logging:
  level: info
  format: json
  output: /var/log/vm-orchestrator/app.log  # or stdout
```

### 5. Security Configuration (Optional)

```yaml
security:
  enable_auth: true
  jwt_secret: "your-secret-key"
  api_keys:
    - "api-key-1"
    - "api-key-2"
  enable_https: true
  cert_file: "/path/to/cert.pem"
  key_file: "/path/to/key.pem"
```

## Running the Application

### Development Mode

```bash
./orchestrator
```

### Production Mode

1. Create a systemd service file:
```bash
sudo nano /etc/systemd/system/vm-orchestrator.service
```

2. Add the following content:
```ini
[Unit]
Description=VM Orchestrator
After=network.target

[Service]
Type=simple
User=vm-orchestrator
WorkingDirectory=/opt/vm-orchestrator
ExecStart=/opt/vm-orchestrator/orchestrator
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

3. Create user and directories:
```bash
sudo useradd -r -s /bin/false vm-orchestrator
sudo mkdir -p /opt/vm-orchestrator
sudo mkdir -p /var/log/vm-orchestrator
sudo chown vm-orchestrator:vm-orchestrator /var/log/vm-orchestrator
```

4. Copy files and set permissions:
```bash
sudo cp orchestrator /opt/vm-orchestrator/
sudo cp config.yaml /opt/vm-orchestrator/
sudo chown -R vm-orchestrator:vm-orchestrator /opt/vm-orchestrator
sudo chmod +x /opt/vm-orchestrator/orchestrator
```

5. Start and enable the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable vm-orchestrator
sudo systemctl start vm-orchestrator
```

6. Check service status:
```bash
sudo systemctl status vm-orchestrator
```

## Docker Deployment

### 1. Build Docker Image

```bash
docker build -t vm-orchestrator .
```

### 2. Run Container

```bash
docker run -d \
  --name vm-orchestrator \
  -p 8080:8080 \
  -v $(pwd)/config.yaml:/app/config.yaml \
  vm-orchestrator
```

### 3. Docker Compose

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  vm-orchestrator:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./config.yaml:/app/config.yaml
      - ./logs:/app/logs
    restart: unless-stopped
```

Run with:
```bash
docker-compose up -d
```

## Verification

### 1. Check Health

```bash
curl http://localhost:8080/health
```

Expected response:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "hypervisors": 2,
    "version": "1.0.0"
  }
}
```

### 2. List Hypervisors

```bash
curl http://localhost:8080/api/v1/hypervisors
```

### 3. List VMs

```bash
curl http://localhost:8080/api/v1/vms
```

## Troubleshooting

### Common Issues

1. **Connection Failed to Hypervisor**
   - Verify network connectivity
   - Check credentials
   - Ensure hypervisor API is enabled
   - Check firewall settings

2. **Certificate Errors**
   - Set `insecure: true` for testing
   - Install proper certificates for production

3. **Permission Denied**
   - Verify user has sufficient privileges
   - Check file permissions

4. **Port Already in Use**
   - Change port in configuration
   - Kill process using the port

### Log Analysis

Check logs for detailed error information:
```bash
# If using systemd
sudo journalctl -u vm-orchestrator -f

# If using file logging
tail -f /var/log/vm-orchestrator/app.log
```

### Debug Mode

Run in debug mode for verbose logging:
```yaml
server:
  mode: debug
logging:
  level: debug
```

## Performance Tuning

### Resource Limits

For production deployments, consider:
- CPU: 2+ cores recommended
- Memory: 1GB+ RAM recommended
- Network: Low latency connection to hypervisors

### Configuration Optimization

```yaml
server:
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 120
```

### Monitoring

Set up monitoring for:
- API response times
- Hypervisor connection status
- Resource usage
- Error rates

## Security Best Practices

1. **Use HTTPS in Production**
2. **Enable Authentication**
3. **Rotate API Keys Regularly**
4. **Use Strong Passwords**
5. **Network Segmentation**
6. **Regular Updates**
7. **Audit Logs**

## Next Steps

- Review the [API Documentation](API.md)
- Check out [Examples](../examples/)
- Set up monitoring and alerting
- Configure backup and disaster recovery
