package api

import (
	"vm-orchestrator/internal/logging"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.Engine, handler *Handler) {
	// Add middleware
	router.Use(logging.RequestIDMiddleware())

	// Health check endpoint
	router.GET("/health", handler.Health)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Hypervisor routes
		hypervisors := v1.Group("/hypervisors")
		{
			hypervisors.GET("", handler.GetHypervisors)
		}

		// VM routes
		vms := v1.Group("/vms")
		{
			vms.GET("", handler.ListVMs)
			vms.POST("", handler.CreateVM)
		}

		// VM routes with hypervisor and VM ID
		vmRoutes := v1.Group("/hypervisors/:hypervisor_id/vms")
		{
			vmRoutes.GET("", handler.ListVMs)
			vmRoutes.POST("", handler.CreateVM)
			vmRoutes.GET("/:vm_id", handler.GetVM)
			vmRoutes.PUT("/:vm_id", handler.UpdateVM)
			vmRoutes.DELETE("/:vm_id", handler.DeleteVM)
			vmRoutes.POST("/:vm_id/actions", handler.VMAction)
			vmRoutes.GET("/:vm_id/metrics", handler.GetVMMetrics)
			vmRoutes.GET("/:vm_id/console", handler.GetVMConsole)
		}

		// Host routes
		hosts := v1.Group("/hypervisors/:hypervisor_id/hosts")
		{
			hosts.GET("", handler.ListHosts)
			hosts.GET("/:host_id", handler.GetHost)
			hosts.GET("/:host_id/metrics", handler.GetHostMetrics)
		}

		// Template routes
		templates := v1.Group("/hypervisors/:hypervisor_id/templates")
		{
			templates.GET("", handler.ListTemplates)
			templates.POST("", handler.CreateTemplate)
			templates.GET("/:template_id", handler.GetTemplate)
			templates.PUT("/:template_id", handler.UpdateTemplate)
			templates.DELETE("/:template_id", handler.DeleteTemplate)
			templates.POST("/:template_id/deploy", handler.DeployTemplate)
		}

		// Snapshot routes
		snapshots := v1.Group("/hypervisors/:hypervisor_id/vms/:vm_id/snapshots")
		{
			snapshots.GET("", handler.ListSnapshots)
			snapshots.POST("", handler.CreateSnapshot)
			snapshots.GET("/tree", handler.GetSnapshotTree)
			snapshots.GET("/:snapshot_id", handler.GetSnapshot)
			snapshots.POST("/:snapshot_id/restore", handler.RestoreSnapshot)
			snapshots.DELETE("/:snapshot_id", handler.DeleteSnapshot)
		}

		// Storage routes
		storage := v1.Group("/hypervisors/:hypervisor_id/storage")
		{
			storage.GET("/datastores", handler.ListDatastores)
			storage.GET("/datastores/:datastore_id", handler.GetDatastore)
			storage.GET("/networks", handler.ListNetworks)
			storage.GET("/networks/:network_id", handler.GetNetwork)
		}
	}

	// Add API documentation route
	router.GET("/api/docs", func(c *gin.Context) {
		c.JSON(200, getAPIDocumentation())
	})
}

// getAPIDocumentation returns API documentation
func getAPIDocumentation() map[string]interface{} {
	return map[string]interface{}{
		"title":       "VM Orchestrator API",
		"version":     "1.0.0",
		"description": "REST API for managing virtual machines across multiple hypervisors",
		"base_url":    "/api/v1",
		"endpoints": map[string]interface{}{
			"health": map[string]interface{}{
				"GET /health": "Get service health status",
			},
			"hypervisors": map[string]interface{}{
				"GET /api/v1/hypervisors": "List all connected hypervisors",
			},
			"vms": map[string]interface{}{
				"GET /api/v1/vms":                                      "List all VMs across all hypervisors",
				"POST /api/v1/vms":                                     "Create a new VM",
				"GET /api/v1/hypervisors/{hypervisor_id}/vms":          "List VMs on specific hypervisor",
				"POST /api/v1/hypervisors/{hypervisor_id}/vms":         "Create VM on specific hypervisor",
				"GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}":  "Get specific VM",
				"PUT /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}":  "Update VM configuration",
				"DELETE /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}": "Delete VM",
				"POST /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/actions": "Perform VM action (start/stop/restart/suspend/resume)",
				"GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/metrics": "Get VM performance metrics",
				"GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/console": "Get VM console URL",
			},
			"hosts": map[string]interface{}{
				"GET /api/v1/hypervisors/{hypervisor_id}/hosts":                    "List hosts",
				"GET /api/v1/hypervisors/{hypervisor_id}/hosts/{host_id}":          "Get specific host",
				"GET /api/v1/hypervisors/{hypervisor_id}/hosts/{host_id}/metrics":  "Get host metrics",
			},
			"templates": map[string]interface{}{
				"GET /api/v1/hypervisors/{hypervisor_id}/templates":                        "List templates",
				"POST /api/v1/hypervisors/{hypervisor_id}/templates":                       "Create template",
				"GET /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}":          "Get specific template",
				"PUT /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}":          "Update template",
				"DELETE /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}":       "Delete template",
				"POST /api/v1/hypervisors/{hypervisor_id}/templates/{template_id}/deploy":  "Deploy VM from template",
			},
			"snapshots": map[string]interface{}{
				"GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots":                        "List VM snapshots",
				"POST /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots":                       "Create snapshot",
				"GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots/tree":                   "Get snapshot tree",
				"GET /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots/{snapshot_id}":          "Get specific snapshot",
				"POST /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots/{snapshot_id}/restore": "Restore snapshot",
				"DELETE /api/v1/hypervisors/{hypervisor_id}/vms/{vm_id}/snapshots/{snapshot_id}":       "Delete snapshot",
			},
			"storage": map[string]interface{}{
				"GET /api/v1/hypervisors/{hypervisor_id}/storage/datastores":                   "List datastores",
				"GET /api/v1/hypervisors/{hypervisor_id}/storage/datastores/{datastore_id}":   "Get specific datastore",
				"GET /api/v1/hypervisors/{hypervisor_id}/storage/networks":                     "List networks",
				"GET /api/v1/hypervisors/{hypervisor_id}/storage/networks/{network_id}":       "Get specific network",
			},
		},
		"query_parameters": map[string]interface{}{
			"vms": map[string]string{
				"hypervisor_id": "Filter by hypervisor ID",
				"name":          "Filter by VM name (partial match)",
				"state":         "Filter by VM state (poweredOn, poweredOff, suspended)",
				"limit":         "Limit number of results",
				"offset":        "Offset for pagination",
			},
		},
		"request_examples": map[string]interface{}{
			"create_vm": map[string]interface{}{
				"name":          "test-vm",
				"hypervisor_id": "esxi-host-1",
				"cpu": map[string]int{
					"cores":   2,
					"sockets": 1,
					"threads": 1,
				},
				"memory": map[string]int{
					"size_mb": 2048,
				},
				"disks": []map[string]interface{}{
					{
						"name":      "disk0",
						"size_gb":   20,
						"datastore": "datastore1",
						"type":      "thin",
					},
				},
				"networks": []map[string]interface{}{
					{
						"name":    "net0",
						"network": "VM Network",
						"type":    "vmxnet3",
					},
				},
			},
			"vm_action": map[string]interface{}{
				"action": "start",
				"force":  false,
			},
			"create_snapshot": map[string]interface{}{
				"name":        "snapshot-1",
				"description": "Test snapshot",
				"memory":      true,
				"quiesce":     false,
			},
		},
	}
}
