# VM Orchestrator

A comprehensive Virtual Machine Orchestrator application that manages and orchestrates virtual machines across both VMware ESXi and Proxmox VE hypervisors.

## Features

### Core Capabilities
- **Multi-hypervisor support** - Connect to and manage both ESXi and Proxmox VE environments
- **VM lifecycle management** - Create, start, stop, restart, suspend, resume, and delete virtual machines
- **Resource monitoring** - Track CPU, memory, disk, and network usage for VMs and hosts
- **Template management** - Create, deploy, and manage VM templates
- **Snapshot operations** - Create, restore, and delete VM snapshots
- **Network management** - Configure virtual networks, VLANs, and network adapters
- **Storage management** - Manage datastores, storage allocation, and disk operations

### Technical Features
- RESTful API with comprehensive endpoints
- Unified interface abstracting hypervisor differences
- Concurrent operations across multiple hosts
- Comprehensive error handling and logging
- Configuration management for connection details
- Async/await patterns for API calls
- Retry mechanisms for failed operations

## Architecture

```
vm-orchestrator/
├── cmd/orchestrator/          # Application entry point
├── internal/
│   ├── api/                   # REST API handlers and routes
│   ├── config/                # Configuration management
│   ├── interfaces/            # Common interfaces and abstractions
│   ├── models/                # Data models and structures
│   ├── hypervisors/           # Hypervisor-specific implementations
│   │   ├── esxi/             # VMware ESXi integration
│   │   └── proxmox/          # Proxmox VE integration
│   ├── orchestrator/          # Main orchestrator service
│   └── logging/               # Logging utilities
├── pkg/                       # Public packages
├── tests/                     # Unit and integration tests
├── docs/                      # Documentation
└── examples/                  # Example scripts and configurations
```

## Quick Start

### Prerequisites
- Go 1.21 or later
- Access to VMware ESXi and/or Proxmox VE environments
- Valid credentials for hypervisor connections

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd vm-orchestrator
```

2. Install dependencies:
```bash
go mod download
```

3. Create configuration file:
```bash
cp config.example.yaml config.yaml
# Edit config.yaml with your hypervisor details
```

4. Build and run:
```bash
go build -o orchestrator cmd/orchestrator/main.go
./orchestrator
```

### Configuration

Create a `config.yaml` file with your hypervisor connection details:

```yaml
server:
  port: 8080
  mode: debug
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60

logging:
  level: info
  format: json
  output: stdout

hypervisors:
  esxi:
    - name: "esxi-host-1"
      host: "*************"
      username: "root"
      password: "password"
      insecure: true
  
  proxmox:
    - name: "proxmox-host-1"
      host: "*************"
      username: "root@pam"
      password: "password"
      insecure: true
```

## API Documentation

The orchestrator provides a comprehensive REST API. Once running, you can access:

- API Base URL: `http://localhost:8080/api/v1`
- Health Check: `GET /health`
- VM Operations: `GET|POST|PUT|DELETE /vms`
- Host Information: `GET /hosts`
- Templates: `GET|POST /templates`
- Snapshots: `GET|POST|DELETE /snapshots`

Detailed API documentation is available in the `docs/` directory.

## Development

### Running Tests
```bash
go test ./...
```

### Building
```bash
go build -o orchestrator cmd/orchestrator/main.go
```

### Docker
```bash
docker build -t vm-orchestrator .
docker run -p 8080:8080 -v $(pwd)/config.yaml:/app/config.yaml vm-orchestrator
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
