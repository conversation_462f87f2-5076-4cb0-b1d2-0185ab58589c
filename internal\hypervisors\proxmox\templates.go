package proxmox

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"
)

// ListTemplates returns a list of VM templates
func (c *Client) ListTemplates(ctx context.Context, filters interfaces.TemplateFilters) ([]models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Get list of nodes
	nodes, err := c.getNodes(ctx)
	if err != nil {
		return nil, err
	}

	var allTemplates []models.Template

	// Get templates from each node
	for _, node := range nodes {
		resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu", node), nil)
		if err != nil {
			continue
		}

		var vms []ProxmoxVM
		if err := c.parseResponse(resp, &vms); err != nil {
			continue
		}

		// Filter for templates (in Proxmox, templates have template=1 in config)
		for _, vm := range vms {
			// Check if VM is a template by getting its config
			configResp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/config", node, vm.VMID), nil)
			if err != nil {
				continue
			}

			var config struct {
				Template int `json:"template"`
			}
			if err := c.parseResponse(configResp, &config); err != nil {
				continue
			}

			if config.Template == 1 {
				template := c.convertProxmoxVMToTemplate(vm, node)
				if c.matchesTemplateFilters(&template, filters) {
					allTemplates = append(allTemplates, template)
				}
			}
		}
	}

	// Apply pagination
	if filters.Limit > 0 {
		start := filters.Offset
		end := start + filters.Limit
		if start >= len(allTemplates) {
			return []models.Template{}, nil
		}
		if end > len(allTemplates) {
			end = len(allTemplates)
		}
		allTemplates = allTemplates[start:end]
	}

	return allTemplates, nil
}

// GetTemplate returns a specific template
func (c *Client) GetTemplate(ctx context.Context, templateID string) (*models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse template ID
	id, err := strconv.Atoi(templateID)
	if err != nil {
		return nil, models.NewValidationError("Invalid template ID", "Template ID must be numeric")
	}

	// Find the node containing this template
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Get template configuration
	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/config", node, id), nil)
	if err != nil {
		return nil, err
	}

	var config ProxmoxVMConfig
	if err := c.parseResponse(resp, &config); err != nil {
		return nil, err
	}

	return c.convertProxmoxVMConfigToTemplate(config, node), nil
}

// CreateTemplate creates a new template from a VM
func (c *Client) CreateTemplate(ctx context.Context, req models.TemplateCreateRequest) (*models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse source VM ID
	sourceID, err := strconv.Atoi(req.SourceVMID)
	if err != nil {
		return nil, models.NewValidationError("Invalid source VM ID", "VM ID must be numeric")
	}

	// Find the node containing the source VM
	node, err := c.findVMNode(ctx, sourceID)
	if err != nil {
		return nil, err
	}

	// Check if VM is powered off
	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu/%d/status/current", node, sourceID), nil)
	if err != nil {
		return nil, err
	}

	var status ProxmoxVMStatus
	if err := c.parseResponse(resp, &status); err != nil {
		return nil, err
	}

	if status.Status != "stopped" {
		return nil, models.NewAPIError(models.ErrorCodeVMInvalidState, "VM must be stopped to create template", "")
	}

	// Get next VM ID for the template
	templateID, err := c.getNextVMID(ctx)
	if err != nil {
		return nil, err
	}

	// Clone VM to create template
	cloneData := url.Values{
		"newid":       {strconv.Itoa(templateID)},
		"name":        {req.Name},
		"description": {req.Description},
		"full":        {"1"}, // Full clone
	}

	resp, err = c.makeRequest(ctx, "POST", fmt.Sprintf("/nodes/%s/qemu/%d/clone", node, sourceID), cloneData)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to clone VM", err.Error())
	}

	if err := c.parseResponse(resp, nil); err != nil {
		return nil, err
	}

	// Wait for clone to complete
	time.Sleep(5 * time.Second)

	// Convert cloned VM to template
	templateData := url.Values{
		"template": {"1"},
	}

	resp, err = c.makeRequest(ctx, "POST", fmt.Sprintf("/nodes/%s/qemu/%d/template", node, templateID), templateData)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to convert VM to template", err.Error())
	}

	if err := c.parseResponse(resp, nil); err != nil {
		return nil, err
	}

	return c.GetTemplate(ctx, strconv.Itoa(templateID))
}

// UpdateTemplate updates a template
func (c *Client) UpdateTemplate(ctx context.Context, templateID string, req models.TemplateUpdateRequest) (*models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse template ID
	id, err := strconv.Atoi(templateID)
	if err != nil {
		return nil, models.NewValidationError("Invalid template ID", "Template ID must be numeric")
	}

	// Find the node containing this template
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Build update configuration
	config := url.Values{}
	
	if req.Name != nil {
		config.Set("name", *req.Name)
	}
	
	if req.Description != nil {
		config.Set("description", *req.Description)
	}

	// Update template
	resp, err := c.makeRequest(ctx, "PUT", fmt.Sprintf("/nodes/%s/qemu/%d/config", node, id), config)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to update template", err.Error())
	}

	if err := c.parseResponse(resp, nil); err != nil {
		return nil, err
	}

	return c.GetTemplate(ctx, templateID)
}

// DeleteTemplate deletes a template
func (c *Client) DeleteTemplate(ctx context.Context, templateID string) error {
	if !c.connected {
		return models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse template ID
	id, err := strconv.Atoi(templateID)
	if err != nil {
		return models.NewValidationError("Invalid template ID", "Template ID must be numeric")
	}

	// Find the node containing this template
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return err
	}

	// Delete template
	resp, err := c.makeRequest(ctx, "DELETE", fmt.Sprintf("/nodes/%s/qemu/%d", node, id), nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to delete template", err.Error())
	}

	return c.parseResponse(resp, nil)
}

// DeployTemplate deploys a VM from a template
func (c *Client) DeployTemplate(ctx context.Context, templateID string, req models.TemplateDeployRequest) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Parse template ID
	id, err := strconv.Atoi(templateID)
	if err != nil {
		return nil, models.NewValidationError("Invalid template ID", "Template ID must be numeric")
	}

	// Find the node containing this template
	node, err := c.findVMNode(ctx, id)
	if err != nil {
		return nil, err
	}

	// Get next VM ID
	vmid, err := c.getNextVMID(ctx)
	if err != nil {
		return nil, err
	}

	// Clone template to create VM
	cloneData := url.Values{
		"newid": {strconv.Itoa(vmid)},
		"name":  {req.VMName},
		"full":  {"1"}, // Full clone
	}

	resp, err := c.makeRequest(ctx, "POST", fmt.Sprintf("/nodes/%s/qemu/%d/clone", node, id), cloneData)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to deploy template", err.Error())
	}

	if err := c.parseResponse(resp, nil); err != nil {
		return nil, err
	}

	// Wait for clone to complete
	time.Sleep(5 * time.Second)

	// Apply customizations if provided
	if req.CPU != nil || req.Memory != nil {
		config := url.Values{}
		
		if req.CPU != nil {
			config.Set("cores", strconv.Itoa(req.CPU.Cores))
			config.Set("sockets", strconv.Itoa(req.CPU.Sockets))
		}
		
		if req.Memory != nil {
			config.Set("memory", strconv.Itoa(req.Memory.SizeMB))
		}

		resp, err = c.makeRequest(ctx, "PUT", fmt.Sprintf("/nodes/%s/qemu/%d/config", node, vmid), config)
		if err != nil {
			return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to customize VM", err.Error())
		}

		if err := c.parseResponse(resp, nil); err != nil {
			return nil, err
		}
	}

	// Power on VM if requested
	if req.PowerOn {
		c.StartVM(ctx, strconv.Itoa(vmid))
	}

	return c.GetVM(ctx, strconv.Itoa(vmid))
}

// convertProxmoxVMToTemplate converts a Proxmox VM to our template model
func (c *Client) convertProxmoxVMToTemplate(vm ProxmoxVM, node string) models.Template {
	return models.Template{
		ID:           strconv.Itoa(vm.VMID),
		Name:         vm.Name,
		Description:  "",
		HypervisorID: c.config.ID,
		Hypervisor:   "proxmox",
		OS:           "linux", // Default, would need config to determine actual OS
		Version:      "",
		CPU: models.CPUConfig{
			Cores:   vm.CPU,
			Sockets: 1,
			Threads: 1,
		},
		Memory: models.MemoryConfig{
			SizeMB: vm.Memory / 1024 / 1024,
		},
		Disks:     []models.DiskConfig{},
		Networks:  []models.NetworkConfig{},
		Tags:      make(map[string]string),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// convertProxmoxVMConfigToTemplate converts detailed VM config to template model
func (c *Client) convertProxmoxVMConfigToTemplate(config ProxmoxVMConfig, node string) *models.Template {
	// Parse disk configuration
	var disks []models.DiskConfig
	if config.SCSI0 != "" {
		disk := c.parseDiskConfig(config.SCSI0, "scsi0")
		if disk != nil {
			disks = append(disks, *disk)
		}
	}

	// Parse network configuration
	var networks []models.NetworkConfig
	if config.Net0 != "" {
		network := c.parseNetworkConfig(config.Net0, "net0")
		if network != nil {
			networks = append(networks, *network)
		}
	}

	return &models.Template{
		ID:           strconv.Itoa(config.VMID),
		Name:         config.Name,
		Description:  config.Description,
		HypervisorID: c.config.ID,
		Hypervisor:   "proxmox",
		OS:           config.OSType,
		Version:      "",
		CPU: models.CPUConfig{
			Cores:   config.Cores,
			Sockets: config.Sockets,
			Threads: 1,
		},
		Memory: models.MemoryConfig{
			SizeMB: config.Memory,
		},
		Disks:     disks,
		Networks:  networks,
		Tags:      make(map[string]string),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

// matchesTemplateFilters checks if a template matches the given filters
func (c *Client) matchesTemplateFilters(template *models.Template, filters interfaces.TemplateFilters) bool {
	// Name filter
	if filters.Name != "" && !strings.Contains(strings.ToLower(template.Name), strings.ToLower(filters.Name)) {
		return false
	}

	// OS filter
	if filters.OS != "" && !strings.Contains(strings.ToLower(template.OS), strings.ToLower(filters.OS)) {
		return false
	}

	// Hypervisor ID filter
	if filters.HypervisorID != "" && template.HypervisorID != filters.HypervisorID {
		return false
	}

	// Tags filter
	if len(filters.Tags) > 0 {
		for key, value := range filters.Tags {
			if templateValue, exists := template.Tags[key]; !exists || templateValue != value {
				return false
			}
		}
	}

	return true
}
