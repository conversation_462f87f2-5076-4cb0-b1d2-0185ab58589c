package esxi

import (
	"context"
	"strings"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/types"
)

// ListTemplates returns a list of VM templates
func (c *Client) ListTemplates(ctx context.Context, filters interfaces.TemplateFilters) ([]models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	// In ESXi, templates are VMs marked as templates
	vms, err := c.finder.VirtualMachineList(ctx, "*")
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list templates", err.Error())
	}

	var result []models.Template
	for _, vm := range vms {
		// Check if VM is a template
		var vmProps struct {
			Config *types.VirtualMachineConfigInfo `mo:"config"`
		}
		err := vm.Properties(ctx, vm.Reference(), []string{"config"}, &vmProps)
		if err != nil {
			continue
		}

		if vmProps.Config.Template {
			template, err := c.convertVMToTemplate(ctx, vm)
			if err != nil {
				continue
			}

			// Apply filters
			if c.matchesTemplateFilters(template, filters) {
				result = append(result, *template)
			}
		}
	}

	// Apply pagination
	if filters.Limit > 0 {
		start := filters.Offset
		end := start + filters.Limit
		if start >= len(result) {
			return []models.Template{}, nil
		}
		if end > len(result) {
			end = len(result)
		}
		result = result[start:end]
	}

	return result, nil
}

// GetTemplate returns a specific template
func (c *Client) GetTemplate(ctx context.Context, templateID string) (*models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, templateID)
	if err != nil {
		return nil, models.NewNotFoundError("Template", templateID)
	}

	// Verify it's a template
	var vmProps struct {
		Config *types.VirtualMachineConfigInfo `mo:"config"`
	}
	err = vm.Properties(ctx, vm.Reference(), []string{"config"}, &vmProps)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get template properties", err.Error())
	}

	if !vmProps.Config.Template {
		return nil, models.NewNotFoundError("Template", templateID)
	}

	return c.convertVMToTemplate(ctx, vm)
}

// CreateTemplate creates a new template from a VM
func (c *Client) CreateTemplate(ctx context.Context, req models.TemplateCreateRequest) (*models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	// Find source VM
	sourceVM, err := c.finder.VirtualMachine(ctx, req.SourceVMID)
	if err != nil {
		return nil, models.NewNotFoundError("VM", req.SourceVMID)
	}

	// Check if VM is powered off
	state, err := sourceVM.PowerState(ctx)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get VM state", err.Error())
	}

	if state != types.VirtualMachinePowerStatePoweredOff {
		return nil, models.NewAPIError(models.ErrorCodeVMInvalidState, "VM must be powered off to create template", "")
	}

	// Clone VM to create template
	cloneSpec := types.VirtualMachineCloneSpec{
		Location: types.VirtualMachineRelocateSpec{
			Transform: types.VirtualMachineRelocateTransformationFlat,
		},
		Template: true,
		PowerOn:  false,
	}

	// Find folder for templates
	folder, err := c.finder.DefaultFolder(ctx)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to find folder", err.Error())
	}

	// Clone VM
	task, err := sourceVM.Clone(ctx, folder, req.Name, cloneSpec)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to create template", err.Error())
	}

	// Wait for task completion
	info, err := task.WaitForResult(ctx, nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Template creation task failed", err.Error())
	}

	// Get created template
	templateVM := object.NewVirtualMachine(c.client.Client, info.Result.(types.ManagedObjectReference))
	return c.convertVMToTemplate(ctx, templateVM)
}

// UpdateTemplate updates a template
func (c *Client) UpdateTemplate(ctx context.Context, templateID string, req models.TemplateUpdateRequest) (*models.Template, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, templateID)
	if err != nil {
		return nil, models.NewNotFoundError("Template", templateID)
	}

	// Build configuration spec
	spec := types.VirtualMachineConfigSpec{}

	if req.Name != nil {
		spec.Name = *req.Name
	}

	if req.Description != nil {
		spec.Annotation = *req.Description
	}

	// Reconfigure template
	task, err := vm.Reconfigure(ctx, spec)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to update template", err.Error())
	}

	// Wait for task completion
	_, err = task.WaitForResult(ctx, nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Template update task failed", err.Error())
	}

	return c.convertVMToTemplate(ctx, vm)
}

// DeleteTemplate deletes a template
func (c *Client) DeleteTemplate(ctx context.Context, templateID string) error {
	if !c.connected {
		return models.NewConnectionError("ESXi", "Not connected")
	}

	vm, err := c.finder.VirtualMachine(ctx, templateID)
	if err != nil {
		return models.NewNotFoundError("Template", templateID)
	}

	// Delete template
	task, err := vm.Destroy(ctx)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to delete template", err.Error())
	}

	_, err = task.WaitForResult(ctx, nil)
	if err != nil {
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, "Template deletion task failed", err.Error())
	}

	return nil
}

// DeployTemplate deploys a VM from a template
func (c *Client) DeployTemplate(ctx context.Context, templateID string, req models.TemplateDeployRequest) (*models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("ESXi", "Not connected")
	}

	// Find template
	template, err := c.finder.VirtualMachine(ctx, templateID)
	if err != nil {
		return nil, models.NewNotFoundError("Template", templateID)
	}

	// Find resource pool
	pool, err := c.finder.DefaultResourcePool(ctx)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to find resource pool", err.Error())
	}

	// Find folder
	folder, err := c.finder.DefaultFolder(ctx)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to find folder", err.Error())
	}

	// Build clone specification
	cloneSpec := types.VirtualMachineCloneSpec{
		Location: types.VirtualMachineRelocateSpec{
			Pool: types.NewReference(pool.Reference()),
		},
		Template: false,
		PowerOn:  req.PowerOn,
	}

	// Apply customizations if provided
	if req.CPU != nil || req.Memory != nil {
		config := types.VirtualMachineConfigSpec{}

		if req.CPU != nil {
			config.NumCPUs = int32(req.CPU.Cores)
		}

		if req.Memory != nil {
			config.MemoryMB = int64(req.Memory.SizeMB)
		}

		cloneSpec.Config = &config
	}

	// Clone template to create VM
	task, err := template.Clone(ctx, folder, req.VMName, cloneSpec)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to deploy template", err.Error())
	}

	// Wait for task completion
	info, err := task.WaitForResult(ctx, nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Template deployment task failed", err.Error())
	}

	// Get created VM
	vm := object.NewVirtualMachine(c.client.Client, info.Result.(types.ManagedObjectReference))
	return c.convertVMToModel(ctx, vm)
}

// convertVMToTemplate converts a VM object to a template model
func (c *Client) convertVMToTemplate(ctx context.Context, vm *object.VirtualMachine) (*models.Template, error) {
	var vmProps struct {
		Config *types.VirtualMachineConfigInfo `mo:"config"`
	}
	err := vm.Properties(ctx, vm.Reference(), []string{"config"}, &vmProps)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get template properties", err.Error())
	}

	config := vmProps.Config

	// Extract CPU configuration
	cpuConfig := models.CPUConfig{
		Cores:   int(config.Hardware.NumCPU),
		Sockets: int(config.Hardware.NumCPU), // Simplified
		Threads: 1,
	}

	// Extract memory configuration
	memoryConfig := models.MemoryConfig{
		SizeMB: int(config.Hardware.MemoryMB),
	}

	// Extract disk configurations
	var disks []models.DiskConfig
	for _, device := range config.Hardware.Device {
		if disk, ok := device.(*types.VirtualDisk); ok {
			diskConfig := models.DiskConfig{
				ID:     disk.DeviceInfo.GetDescription().Label,
				Name:   disk.DeviceInfo.GetDescription().Label,
				SizeGB: int(disk.CapacityInKB / 1024 / 1024),
				Type:   "thick",
			}

			if backing, ok := disk.Backing.(*types.VirtualDiskFlatVer2BackingInfo); ok {
				if backing.ThinProvisioned != nil && *backing.ThinProvisioned {
					diskConfig.Type = "thin"
				}
			}

			disks = append(disks, diskConfig)
		}
	}

	// Extract network configurations
	var networks []models.NetworkConfig
	for _, device := range config.Hardware.Device {
		if nic, ok := device.(types.BaseVirtualEthernetCard); ok {
			card := nic.GetVirtualEthernetCard()
			networkConfig := models.NetworkConfig{
				ID:   card.DeviceInfo.GetDescription().Label,
				Name: card.DeviceInfo.GetDescription().Label,
				Type: "vmxnet3",
			}

			if backing, ok := card.Backing.(*types.VirtualEthernetCardNetworkBackingInfo); ok {
				networkConfig.Network = backing.DeviceName
			}

			networks = append(networks, networkConfig)
		}
	}

	template := &models.Template{
		ID:           vm.Reference().Value,
		Name:         config.Name,
		Description:  config.Annotation,
		HypervisorID: c.config.ID,
		Hypervisor:   "esxi",
		OS:           config.GuestFullName,
		Version:      "", // Would need additional logic to extract version
		CPU:          cpuConfig,
		Memory:       memoryConfig,
		Disks:        disks,
		Networks:     networks,
		Tags:         make(map[string]string),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	return template, nil
}

// matchesTemplateFilters checks if a template matches the given filters
func (c *Client) matchesTemplateFilters(template *models.Template, filters interfaces.TemplateFilters) bool {
	// Name filter
	if filters.Name != "" && !strings.Contains(strings.ToLower(template.Name), strings.ToLower(filters.Name)) {
		return false
	}

	// OS filter
	if filters.OS != "" && !strings.Contains(strings.ToLower(template.OS), strings.ToLower(filters.OS)) {
		return false
	}

	// Hypervisor ID filter
	if filters.HypervisorID != "" && template.HypervisorID != filters.HypervisorID {
		return false
	}

	// Tags filter
	if len(filters.Tags) > 0 {
		for key, value := range filters.Tags {
			if templateValue, exists := template.Tags[key]; !exists || templateValue != value {
				return false
			}
		}
	}

	return true
}
