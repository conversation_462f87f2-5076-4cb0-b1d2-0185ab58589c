package proxmox

import (
	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"
)

// Factory implements the HypervisorFactory interface for Proxmox VE
type Factory struct{}

// NewFactory creates a new Proxmox factory
func NewFactory() *Factory {
	return &Factory{}
}

// CreateClient creates a new Proxmox client with the given configuration
func (f *Factory) CreateClient(config interfaces.HypervisorConfig) (interfaces.HypervisorClient, error) {
	if config.Type != "proxmox" {
		return nil, models.NewValidationError("Invalid hypervisor type", "Expected 'proxmox', got '"+config.Type+"'")
	}

	// Validate required configuration
	if config.Host == "" {
		return nil, models.NewValidationError("Host is required", "")
	}
	if config.Username == "" {
		return nil, models.NewValidationError("Username is required", "")
	}
	if config.Password == "" {
		return nil, models.NewValidationError("Password is required", "")
	}

	// Set default port if not specified
	if config.Port == 0 {
		config.Port = 8006
	}

	// Set default timeout if not specified
	if config.Timeout == 0 {
		config.Timeout = 30
	}

	return NewClient(config), nil
}

// GetSupportedTypes returns the supported hypervisor types
func (f *Factory) GetSupportedTypes() []string {
	return []string{"proxmox"}
}
