package models

import "time"

// Template represents a VM template
type Template struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Description  string            `json:"description"`
	HypervisorID string            `json:"hypervisor_id"`
	Hypervisor   string            `json:"hypervisor"` // "esxi" or "proxmox"
	OS           string            `json:"os"`
	Version      string            `json:"version"`
	CPU          CPUConfig         `json:"cpu"`
	Memory       MemoryConfig      `json:"memory"`
	Disks        []DiskConfig      `json:"disks"`
	Networks     []NetworkConfig   `json:"networks"`
	Tags         map[string]string `json:"tags,omitempty"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// TemplateCreateRequest represents a request to create a template
type TemplateCreateRequest struct {
	Name         string            `json:"name" binding:"required"`
	Description  string            `json:"description"`
	HypervisorID string            `json:"hypervisor_id" binding:"required"`
	SourceVMID   string            `json:"source_vm_id" binding:"required"`
	OS           string            `json:"os"`
	Version      string            `json:"version"`
	Tags         map[string]string `json:"tags,omitempty"`
}

// TemplateUpdateRequest represents a request to update a template
type TemplateUpdateRequest struct {
	Name        *string           `json:"name,omitempty"`
	Description *string           `json:"description,omitempty"`
	OS          *string           `json:"os,omitempty"`
	Version     *string           `json:"version,omitempty"`
	Tags        map[string]string `json:"tags,omitempty"`
}

// TemplateDeployRequest represents a request to deploy a VM from template
type TemplateDeployRequest struct {
	VMName       string            `json:"vm_name" binding:"required"`
	HypervisorID string            `json:"hypervisor_id" binding:"required"`
	CPU          *CPUConfig        `json:"cpu,omitempty"`
	Memory       *MemoryConfig     `json:"memory,omitempty"`
	Networks     []NetworkConfig   `json:"networks,omitempty"`
	Tags         map[string]string `json:"tags,omitempty"`
	PowerOn      bool              `json:"power_on"`
}

// TemplateListResponse represents a paginated list of templates
type TemplateListResponse struct {
	Templates  []Template `json:"templates"`
	Total      int        `json:"total"`
	Page       int        `json:"page"`
	PageSize   int        `json:"page_size"`
	TotalPages int        `json:"total_pages"`
}
