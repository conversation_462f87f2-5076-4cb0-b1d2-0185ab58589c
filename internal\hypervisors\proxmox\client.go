package proxmox

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"
)

// Client implements the HypervisorClient interface for Proxmox VE
type Client struct {
	config     interfaces.HypervisorConfig
	httpClient *http.Client
	baseURL    string
	ticket     string
	csrfToken  string
	connected  bool
}

// NewClient creates a new Proxmox client
func NewClient(config interfaces.HypervisorConfig) *Client {
	// Create HTTP client with custom transport for SSL settings
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: config.Insecure,
		},
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.Timeout) * time.Second,
	}

	// Build base URL
	port := config.Port
	if port == 0 {
		port = 8006 // Default Proxmox port
	}
	baseURL := fmt.Sprintf("https://%s:%d/api2/json", config.Host, port)

	return &Client{
		config:     config,
		httpClient: httpClient,
		baseURL:    baseURL,
		connected:  false,
	}
}

// Connect establishes connection to Proxmox VE
func (c *Client) Connect(ctx context.Context) error {
	// Prepare authentication request
	authData := url.Values{
		"username": {c.config.Username},
		"password": {c.config.Password},
	}

	// Make authentication request
	resp, err := c.httpClient.PostForm(c.baseURL+"/access/ticket", authData)
	if err != nil {
		return models.NewConnectionError("Proxmox", fmt.Sprintf("Authentication request failed: %v", err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return models.NewConnectionError("Proxmox", fmt.Sprintf("Authentication failed with status: %d", resp.StatusCode))
	}

	// Parse authentication response
	var authResp struct {
		Data struct {
			Ticket    string `json:"ticket"`
			CSRFToken string `json:"CSRFPreventionToken"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return models.NewConnectionError("Proxmox", fmt.Sprintf("Failed to parse auth response: %v", err))
	}

	c.ticket = authResp.Data.Ticket
	c.csrfToken = authResp.Data.CSRFToken
	c.connected = true

	return nil
}

// Disconnect closes the connection to Proxmox VE
func (c *Client) Disconnect(ctx context.Context) error {
	c.ticket = ""
	c.csrfToken = ""
	c.connected = false
	return nil
}

// IsConnected returns the connection status
func (c *Client) IsConnected() bool {
	return c.connected && c.ticket != ""
}

// GetInfo returns hypervisor information
func (c *Client) GetInfo() *interfaces.HypervisorInfo {
	info := &interfaces.HypervisorInfo{
		ID:      c.config.ID,
		Name:    c.config.Name,
		Type:    "proxmox",
		Address: c.config.Host,
	}

	if c.connected {
		// Get version info from API
		version, err := c.getVersion(context.Background())
		if err == nil {
			info.Version = version
		}
	}

	return info
}

// makeRequest makes an authenticated HTTP request to Proxmox API
func (c *Client) makeRequest(ctx context.Context, method, path string, data interface{}) (*http.Response, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	var body io.Reader
	var contentType string

	if data != nil {
		switch v := data.(type) {
		case url.Values:
			body = strings.NewReader(v.Encode())
			contentType = "application/x-www-form-urlencoded"
		default:
			jsonData, err := json.Marshal(data)
			if err != nil {
				return nil, models.NewAPIError(models.ErrorCodeInvalidRequest, "Failed to marshal request data", err.Error())
			}
			body = bytes.NewReader(jsonData)
			contentType = "application/json"
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, c.baseURL+path, body)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeInternal, "Failed to create request", err.Error())
	}

	// Set authentication headers
	req.Header.Set("Cookie", "PVEAuthCookie="+c.ticket)
	if method != "GET" {
		req.Header.Set("CSRFPreventionToken", c.csrfToken)
	}

	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, models.NewConnectionError("Proxmox", fmt.Sprintf("Request failed: %v", err))
	}

	return resp, nil
}

// parseResponse parses a Proxmox API response
func (c *Client) parseResponse(resp *http.Response, result interface{}) error {
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		var errorResp struct {
			Errors interface{} `json:"errors"`
		}
		json.NewDecoder(resp.Body).Decode(&errorResp)
		return models.NewAPIError(models.ErrorCodeVMOperationFailed, fmt.Sprintf("API error: %d", resp.StatusCode), fmt.Sprintf("%v", errorResp.Errors))
	}

	if result != nil {
		var apiResp struct {
			Data interface{} `json:"data"`
		}
		apiResp.Data = result

		if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
			return models.NewAPIError(models.ErrorCodeInternal, "Failed to parse response", err.Error())
		}
	}

	return nil
}

// getVersion gets the Proxmox VE version
func (c *Client) getVersion(ctx context.Context) (string, error) {
	resp, err := c.makeRequest(ctx, "GET", "/version", nil)
	if err != nil {
		return "", err
	}

	var versionInfo struct {
		Version string `json:"version"`
		Release string `json:"release"`
	}

	if err := c.parseResponse(resp, &versionInfo); err != nil {
		return "", err
	}

	return fmt.Sprintf("%s-%s", versionInfo.Version, versionInfo.Release), nil
}

// ListVMs returns a list of virtual machines
func (c *Client) ListVMs(ctx context.Context, filters interfaces.VMFilters) ([]models.VirtualMachine, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Get list of nodes first
	nodes, err := c.getNodes(ctx)
	if err != nil {
		return nil, err
	}

	var allVMs []models.VirtualMachine

	// Get VMs from each node
	for _, node := range nodes {
		resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/qemu", node), nil)
		if err != nil {
			continue // Skip nodes that can't be accessed
		}

		var vms []ProxmoxVM
		if err := c.parseResponse(resp, &vms); err != nil {
			continue
		}

		// Convert to our VM model
		for _, vm := range vms {
			vmModel := c.convertProxmoxVMToModel(vm, node)
			
			// Apply filters
			if c.matchesFilters(&vmModel, filters) {
				allVMs = append(allVMs, vmModel)
			}
		}
	}

	// Apply pagination
	if filters.Limit > 0 {
		start := filters.Offset
		end := start + filters.Limit
		if start >= len(allVMs) {
			return []models.VirtualMachine{}, nil
		}
		if end > len(allVMs) {
			end = len(allVMs)
		}
		allVMs = allVMs[start:end]
	}

	return allVMs, nil
}

// getNodes gets the list of Proxmox nodes
func (c *Client) getNodes(ctx context.Context) ([]string, error) {
	resp, err := c.makeRequest(ctx, "GET", "/nodes", nil)
	if err != nil {
		return nil, err
	}

	var nodes []struct {
		Node string `json:"node"`
	}

	if err := c.parseResponse(resp, &nodes); err != nil {
		return nil, err
	}

	var nodeNames []string
	for _, node := range nodes {
		nodeNames = append(nodeNames, node.Node)
	}

	return nodeNames, nil
}

// ProxmoxVM represents a VM as returned by Proxmox API
type ProxmoxVM struct {
	VMID   int    `json:"vmid"`
	Name   string `json:"name"`
	Status string `json:"status"`
	CPU    int    `json:"cpus"`
	Memory int    `json:"maxmem"`
	Disk   int    `json:"maxdisk"`
	Uptime int    `json:"uptime"`
}
