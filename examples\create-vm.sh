#!/bin/bash

# VM Orchestrator - Create VM Example
# This script demonstrates how to create a new virtual machine

set -e

# Configuration
API_BASE_URL="http://localhost:8080/api/v1"
HYPERVISOR_ID="esxi-host-1"

# VM Configuration
VM_NAME="example-vm"
CPU_CORES=2
CPU_SOCKETS=1
MEMORY_MB=2048
DISK_SIZE_GB=20
DATASTORE="datastore1"
NETWORK="VM Network"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if API is available
check_api() {
    print_status "Checking API availability..."
    if curl -s -f "${API_BASE_URL}/../health" > /dev/null; then
        print_status "API is available"
    else
        print_error "API is not available at ${API_BASE_URL}"
        exit 1
    fi
}

# Function to check if hypervisor exists
check_hypervisor() {
    print_status "Checking hypervisor availability..."
    response=$(curl -s "${API_BASE_URL}/hypervisors")
    if echo "$response" | jq -e ".data[] | select(.id == \"$HYPERVISOR_ID\")" > /dev/null; then
        print_status "Hypervisor $HYPERVISOR_ID is available"
    else
        print_error "Hypervisor $HYPERVISOR_ID not found"
        echo "Available hypervisors:"
        echo "$response" | jq -r '.data[].id'
        exit 1
    fi
}

# Function to create VM
create_vm() {
    print_status "Creating VM: $VM_NAME"
    
    # Create JSON payload
    payload=$(cat <<EOF
{
    "name": "$VM_NAME",
    "hypervisor_id": "$HYPERVISOR_ID",
    "cpu": {
        "cores": $CPU_CORES,
        "sockets": $CPU_SOCKETS,
        "threads": 1
    },
    "memory": {
        "size_mb": $MEMORY_MB
    },
    "disks": [
        {
            "name": "disk0",
            "size_gb": $DISK_SIZE_GB,
            "datastore": "$DATASTORE",
            "type": "thin"
        }
    ],
    "networks": [
        {
            "name": "net0",
            "network": "$NETWORK",
            "type": "vmxnet3"
        }
    ]
}
EOF
    )
    
    # Make API request
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "${API_BASE_URL}/vms")
    
    # Check if request was successful
    if echo "$response" | jq -e '.success' > /dev/null; then
        vm_id=$(echo "$response" | jq -r '.data.id')
        print_status "VM created successfully with ID: $vm_id"
        echo "$response" | jq '.data'
        return 0
    else
        print_error "Failed to create VM"
        echo "$response" | jq '.error'
        return 1
    fi
}

# Function to start VM
start_vm() {
    local vm_id=$1
    print_status "Starting VM: $vm_id"
    
    payload='{"action": "start", "force": false}'
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "${API_BASE_URL}/hypervisors/${HYPERVISOR_ID}/vms/${vm_id}/actions")
    
    if echo "$response" | jq -e '.success' > /dev/null; then
        print_status "VM started successfully"
    else
        print_warning "Failed to start VM (this is normal if VM is already running)"
        echo "$response" | jq '.error'
    fi
}

# Function to get VM status
get_vm_status() {
    local vm_id=$1
    print_status "Getting VM status..."
    
    response=$(curl -s "${API_BASE_URL}/hypervisors/${HYPERVISOR_ID}/vms/${vm_id}")
    
    if echo "$response" | jq -e '.success' > /dev/null; then
        state=$(echo "$response" | jq -r '.data.state')
        print_status "VM state: $state"
        echo "$response" | jq '.data | {id, name, state, cpu, memory}'
    else
        print_error "Failed to get VM status"
        echo "$response" | jq '.error'
    fi
}

# Main execution
main() {
    echo "VM Orchestrator - Create VM Example"
    echo "=================================="
    
    # Check prerequisites
    if ! command -v curl &> /dev/null; then
        print_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_error "jq is required but not installed"
        exit 1
    fi
    
    # Execute steps
    check_api
    check_hypervisor
    
    # Create VM
    if create_vm; then
        vm_id=$(curl -s "${API_BASE_URL}/vms?name=${VM_NAME}" | jq -r '.data.vms[0].id')
        
        # Start VM
        start_vm "$vm_id"
        
        # Wait a moment for state to update
        sleep 3
        
        # Get final status
        get_vm_status "$vm_id"
        
        print_status "VM creation completed successfully!"
        print_status "You can access the VM console at: ${API_BASE_URL}/hypervisors/${HYPERVISOR_ID}/vms/${vm_id}/console"
    else
        print_error "VM creation failed"
        exit 1
    fi
}

# Run main function
main "$@"
