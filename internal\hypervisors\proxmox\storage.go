package proxmox

import (
	"context"
	"fmt"

	"vm-orchestrator/internal/interfaces"
	"vm-orchestrator/internal/models"
)

// ListDatastores returns a list of storage locations
func (c *Client) ListDatastores(ctx context.Context) ([]interfaces.DatastoreInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	resp, err := c.makeRequest(ctx, "GET", "/storage", nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to list storage", err.Error())
	}

	var storages []ProxmoxStorage
	if err := c.parseResponse(resp, &storages); err != nil {
		return nil, err
	}

	var datastores []interfaces.DatastoreInfo
	for _, storage := range storages {
		// Get storage status for usage information
		nodes, err := c.getNodes(ctx)
		if err != nil {
			continue
		}

		// Check storage on first available node
		if len(nodes) > 0 {
			statusResp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/storage/%s/status", nodes[0], storage.Storage), nil)
			if err != nil {
				continue
			}

			var status ProxmoxStorageStatus
			if err := c.parseResponse(statusResp, &status); err != nil {
				continue
			}

			datastore := c.convertProxmoxStorageToDatastore(storage, status)
			datastores = append(datastores, datastore)
		}
	}

	return datastores, nil
}

// GetDatastore returns a specific datastore
func (c *Client) GetDatastore(ctx context.Context, datastoreID string) (*interfaces.DatastoreInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Get storage configuration
	resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/storage/%s", datastoreID), nil)
	if err != nil {
		return nil, models.NewNotFoundError("Datastore", datastoreID)
	}

	var storage ProxmoxStorage
	if err := c.parseResponse(resp, &storage); err != nil {
		return nil, err
	}

	// Get storage status
	nodes, err := c.getNodes(ctx)
	if err != nil {
		return nil, err
	}

	if len(nodes) == 0 {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "No nodes available", "")
	}

	statusResp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/storage/%s/status", nodes[0], datastoreID), nil)
	if err != nil {
		return nil, models.NewAPIError(models.ErrorCodeVMOperationFailed, "Failed to get storage status", err.Error())
	}

	var status ProxmoxStorageStatus
	if err := c.parseResponse(statusResp, &status); err != nil {
		return nil, err
	}

	datastore := c.convertProxmoxStorageToDatastore(storage, status)
	return &datastore, nil
}

// ListNetworks returns a list of networks
func (c *Client) ListNetworks(ctx context.Context) ([]interfaces.NetworkInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Get list of nodes
	nodes, err := c.getNodes(ctx)
	if err != nil {
		return nil, err
	}

	var allNetworks []interfaces.NetworkInfo

	// Get networks from each node
	for _, node := range nodes {
		resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/network", node), nil)
		if err != nil {
			continue
		}

		var networks []ProxmoxNetwork
		if err := c.parseResponse(resp, &networks); err != nil {
			continue
		}

		for _, network := range networks {
			networkInfo := c.convertProxmoxNetworkToInfo(network)
			allNetworks = append(allNetworks, networkInfo)
		}
	}

	return allNetworks, nil
}

// GetNetwork returns a specific network
func (c *Client) GetNetwork(ctx context.Context, networkID string) (*interfaces.NetworkInfo, error) {
	if !c.connected {
		return nil, models.NewConnectionError("Proxmox", "Not connected")
	}

	// Get list of nodes and search for the network
	nodes, err := c.getNodes(ctx)
	if err != nil {
		return nil, err
	}

	for _, node := range nodes {
		resp, err := c.makeRequest(ctx, "GET", fmt.Sprintf("/nodes/%s/network/%s", node, networkID), nil)
		if err != nil {
			continue
		}

		var network ProxmoxNetwork
		if err := c.parseResponse(resp, &network); err != nil {
			continue
		}

		networkInfo := c.convertProxmoxNetworkToInfo(network)
		return &networkInfo, nil
	}

	return nil, models.NewNotFoundError("Network", networkID)
}

// ProxmoxStorage represents storage configuration from Proxmox API
type ProxmoxStorage struct {
	Storage string `json:"storage"`
	Type    string `json:"type"`
	Content string `json:"content"`
	Enabled int    `json:"enabled"`
	Shared  int    `json:"shared"`
}

// ProxmoxStorageStatus represents storage status from Proxmox API
type ProxmoxStorageStatus struct {
	Storage string `json:"storage"`
	Type    string `json:"type"`
	Total   int64  `json:"total"`
	Used    int64  `json:"used"`
	Avail   int64  `json:"avail"`
	Active  int    `json:"active"`
}

// ProxmoxNetwork represents network configuration from Proxmox API
type ProxmoxNetwork struct {
	Iface   string `json:"iface"`
	Type    string `json:"type"`
	Active  int    `json:"active"`
	Address string `json:"address"`
	Gateway string `json:"gateway"`
	Netmask string `json:"netmask"`
	Bridge  string `json:"bridge_ports"`
}

// convertProxmoxStorageToDatastore converts Proxmox storage to our datastore model
func (c *Client) convertProxmoxStorageToDatastore(storage ProxmoxStorage, status ProxmoxStorageStatus) interfaces.DatastoreInfo {
	totalGB := int(status.Total / 1024 / 1024 / 1024)
	usedGB := int(status.Used / 1024 / 1024 / 1024)
	freeGB := int(status.Avail / 1024 / 1024 / 1024)
	
	usagePercent := float64(0)
	if totalGB > 0 {
		usagePercent = float64(usedGB) / float64(totalGB) * 100
	}

	return interfaces.DatastoreInfo{
		ID:           storage.Storage,
		Name:         storage.Storage,
		Type:         storage.Type,
		TotalGB:      totalGB,
		FreeGB:       freeGB,
		UsedGB:       usedGB,
		UsagePercent: usagePercent,
		Accessible:   status.Active == 1,
	}
}

// convertProxmoxNetworkToInfo converts Proxmox network to our network model
func (c *Client) convertProxmoxNetworkToInfo(network ProxmoxNetwork) interfaces.NetworkInfo {
	return interfaces.NetworkInfo{
		ID:     network.Iface,
		Name:   network.Iface,
		Type:   network.Type,
		Active: network.Active == 1,
	}
}
