#!/bin/bash

# VM Orchestrator Test Runner
# This script runs all tests and generates coverage reports

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed or not in PATH"
    exit 1
fi

print_info "Go version: $(go version)"

# Change to project root
cd "$(dirname "$0")/.."

print_info "Running tests from: $(pwd)"

# Clean previous test artifacts
print_info "Cleaning previous test artifacts..."
rm -f coverage.out coverage.html

# Download dependencies
print_info "Downloading dependencies..."
go mod download

# Run tests with coverage
print_info "Running tests with coverage..."
go test -v -race -coverprofile=coverage.out ./...

# Check if tests passed
if [ $? -eq 0 ]; then
    print_success "All tests passed!"
else
    print_error "Some tests failed!"
    exit 1
fi

# Generate coverage report
if [ -f coverage.out ]; then
    print_info "Generating coverage report..."
    go tool cover -html=coverage.out -o coverage.html
    
    # Show coverage summary
    coverage_percent=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
    print_info "Total coverage: $coverage_percent"
    
    # Check coverage threshold
    coverage_num=$(echo $coverage_percent | sed 's/%//')
    threshold=70
    
    if (( $(echo "$coverage_num >= $threshold" | bc -l) )); then
        print_success "Coverage meets threshold ($threshold%)"
    else
        print_warning "Coverage below threshold ($threshold%): $coverage_percent"
    fi
    
    print_info "Coverage report generated: coverage.html"
else
    print_warning "No coverage file generated"
fi

# Run linting if golangci-lint is available
if command -v golangci-lint &> /dev/null; then
    print_info "Running linter..."
    golangci-lint run
    if [ $? -eq 0 ]; then
        print_success "Linting passed!"
    else
        print_warning "Linting issues found"
    fi
else
    print_warning "golangci-lint not found, skipping linting"
fi

# Run security scan if gosec is available
if command -v gosec &> /dev/null; then
    print_info "Running security scan..."
    gosec ./...
    if [ $? -eq 0 ]; then
        print_success "Security scan passed!"
    else
        print_warning "Security issues found"
    fi
else
    print_warning "gosec not found, skipping security scan"
fi

print_success "Test run completed!"

# Open coverage report if requested
if [ "$1" = "--open" ]; then
    if [ -f coverage.html ]; then
        print_info "Opening coverage report..."
        if command -v xdg-open &> /dev/null; then
            xdg-open coverage.html
        elif command -v open &> /dev/null; then
            open coverage.html
        else
            print_info "Please open coverage.html in your browser"
        fi
    fi
fi
